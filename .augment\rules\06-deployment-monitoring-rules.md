---
type: "conditional_apply"
description: "Vercel部署和GitHub集成规范"
---

# Vercel部署和GitHub集成规范

## 部署架构设计

### Vercel + GitHub自动部署流程
```typescript
// 部署工作流
const DEPLOYMENT_WORKFLOW = {
  // 开发流程
  development: {
    local: 'npm run dev -> localhost:3000',
    preview: 'git push feature-branch -> Vercel Preview Deploy',
    testing: 'Preview URL -> 功能测试 -> 代码审查'
  },
  
  // 生产部署
  production: {
    merge: 'Pull Request -> main branch',
    auto_deploy: 'GitHub webhook -> Vercel Production Deploy',
    verification: '自动化测试 -> 部署验证 -> 监控检查'
  },
  
  // 回滚策略
  rollback: {
    instant: 'Vercel Dashboard -> Previous Deployment',
    git_revert: 'git revert -> 自动重新部署',
    hotfix: 'hotfix branch -> 紧急部署'
  }
};
```

### 环境配置管理
```typescript
// 环境变量配置
const ENVIRONMENT_CONFIG = {
  // 开发环境
  development: {
    NODE_ENV: 'development',
    NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
    DATABASE_URL: 'postgresql://localhost:5432/mystical_dev',
    REDIS_URL: 'redis://localhost:6379',
    AI_API_KEYS: 'development_keys'
  },
  
  // 预览环境
  preview: {
    NODE_ENV: 'preview',
    NEXT_PUBLIC_APP_URL: 'https://mystical-website-git-feature-username.vercel.app',
    DATABASE_URL: 'postgresql://preview_db_url',
    REDIS_URL: 'redis://preview_redis_url',
    AI_API_KEYS: 'preview_keys'
  },
  
  // 生产环境
  production: {
    NODE_ENV: 'production',
    NEXT_PUBLIC_APP_URL: 'https://mystical-website.com',
    DATABASE_URL: 'postgresql://production_db_url',
    REDIS_URL: 'redis://production_redis_url',
    AI_API_KEYS: 'production_keys',
    SENTRY_DSN: 'production_sentry_dsn',
    UMAMI_WEBSITE_ID: 'production_umami_id'
  }
};
```

## Vercel配置文件

### vercel.json配置
```json
{
  "version": 2,
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "installCommand": "npm ci",
  "devCommand": "npm run dev",
  "regions": ["hkg1", "sin1", "nrt1"],
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    },
    {
      "source": "/images/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/admin",
      "destination": "/admin/dashboard",
      "permanent": false
    }
  ],
  "rewrites": [
    {
      "source": "/sitemap.xml",
      "destination": "/api/sitemap"
    },
    {
      "source": "/robots.txt",
      "destination": "/api/robots"
    }
  ]
}
```

### next.config.js优化配置
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 基础配置
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@prisma/client']
  },
  
  // 图片优化
  images: {
    domains: ['images.unsplash.com', 'cdn.mystical-website.com'],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 31536000
  },
  
  // 国际化
  i18n: {
    locales: ['en', 'zh', 'es', 'pt', 'hi', 'ja', 'de', 'fr', 'it', 'ru', 'ko', 'ar'],
    defaultLocale: 'en',
    localeDetection: true
  },
  
  // 性能优化
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production'
  },
  
  // 安全配置
  headers: async () => [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'Strict-Transport-Security',
          value: 'max-age=31536000; includeSubDomains; preload'
        },
        {
          key: 'Content-Security-Policy',
          value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
        }
      ]
    }
  ],
  
  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // 重定向
  redirects: async () => [
    {
      source: '/old-path',
      destination: '/new-path',
      permanent: true
    }
  ]
};

module.exports = nextConfig;
```

## GitHub Actions集成

### .github/workflows/vercel-deploy.yml
```yaml
name: Vercel Deploy

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  # 代码质量检查
  quality-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run ESLint
        run: npm run lint
      
      - name: Run TypeScript check
        run: npm run type-check
      
      - name: Run tests
        run: npm run test
      
      - name: Build check
        run: npm run build

  # Vercel部署
  deploy:
    needs: quality-check
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./

  # 部署后验证
  post-deploy-check:
    needs: deploy
    runs-on: ubuntu-latest
    steps:
      - name: Health Check
        run: |
          curl -f https://mystical-website.com/api/health || exit 1
      
      - name: Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://mystical-website.com
            https://mystical-website.com/blog
            https://mystical-website.com/products
          configPath: './lighthouserc.json'
```

### package.json脚本配置
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate deploy",
    "db:seed": "tsx prisma/seed.ts",
    "analyze": "cross-env ANALYZE=true next build",
    "postbuild": "next-sitemap"
  }
}
```

## 数据库部署策略

### Supabase集成配置
```typescript
// 数据库连接配置
const DATABASE_CONFIG = {
  // 开发环境 - 本地PostgreSQL
  development: {
    provider: 'postgresql',
    url: process.env.DATABASE_URL || 'postgresql://localhost:5432/mystical_dev',
    shadowDatabaseUrl: process.env.SHADOW_DATABASE_URL
  },
  
  // 生产环境 - Supabase
  production: {
    provider: 'postgresql',
    url: process.env.DATABASE_URL, // Supabase连接字符串
    directUrl: process.env.DIRECT_URL, // Supabase直连URL
    shadowDatabaseUrl: process.env.SHADOW_DATABASE_URL
  }
};

// Prisma配置
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}
```

### 数据库迁移策略
```bash
# 开发环境迁移
npm run db:generate  # 生成Prisma客户端
npm run db:push      # 推送schema变更到开发数据库

# 生产环境迁移
npm run db:migrate   # 运行生产迁移
npm run db:seed      # 初始化数据（仅首次）
```

## 监控和分析配置

### Sentry错误监控
```typescript
// sentry.client.config.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  debug: process.env.NODE_ENV === 'development',
  integrations: [
    new Sentry.BrowserTracing({
      tracePropagationTargets: ['localhost', /^https:\/\/mystical-website\.com/],
    }),
  ],
});

// sentry.server.config.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
});
```

### Umami分析集成
```typescript
// components/Analytics.tsx
import Script from 'next/script';

export default function Analytics() {
  if (process.env.NODE_ENV !== 'production') return null;
  
  return (
    <Script
      async
      src="https://umami.mystical-website.com/script.js"
      data-website-id={process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID}
    />
  );
}
```

## 性能优化配置

### 缓存策略
```typescript
// 静态资源缓存
const CACHE_HEADERS = {
  // 静态资源 - 1年缓存
  static: 'public, max-age=31536000, immutable',
  
  // 图片资源 - 1年缓存
  images: 'public, max-age=31536000, immutable',
  
  // API响应 - 短期缓存
  api: 'public, max-age=300, s-maxage=300',
  
  // 页面缓存 - ISR
  pages: 'public, max-age=0, s-maxage=86400, stale-while-revalidate'
};
```

### Bundle分析配置
```javascript
// next.config.js中添加
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer(nextConfig);
```

## 安全配置

### 环境变量安全
```bash
# .env.local (本地开发，不提交到Git)
DATABASE_URL="postgresql://localhost:5432/mystical_dev"
NEXTAUTH_SECRET="your-secret-key"
QWEN_API_KEY="your-qwen-api-key"

# Vercel环境变量（在Vercel Dashboard配置）
DATABASE_URL="supabase-connection-string"
NEXTAUTH_SECRET="production-secret"
QWEN_API_KEY="production-qwen-key"
SENTRY_DSN="sentry-dsn"
```

### 安全头配置
```typescript
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // 安全头
  response.headers.set('X-DNS-Prefetch-Control', 'on');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('X-Frame-Options', 'SAMEORIGIN');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin');
  
  return response;
}
```

## 部署检查清单

### 部署前检查
- [ ] 环境变量配置完成
- [ ] 数据库连接测试通过
- [ ] 构建过程无错误
- [ ] TypeScript类型检查通过
- [ ] ESLint检查通过
- [ ] 单元测试通过
- [ ] 性能测试达标

### 部署后验证
- [ ] 网站可正常访问
- [ ] API接口响应正常
- [ ] 数据库连接正常
- [ ] 图片加载正常
- [ ] 多语言切换正常
- [ ] SEO元数据正确
- [ ] 监控系统正常工作

## 故障排除指南

### 常见部署问题
1. **构建失败** - 检查依赖版本、环境变量、TypeScript错误
2. **数据库连接失败** - 验证连接字符串、网络权限
3. **API超时** - 检查函数执行时间、优化查询
4. **图片加载失败** - 验证图片域名配置、CDN设置
5. **环境变量未生效** - 确认Vercel环境变量配置

### 监控告警设置
- **错误率超过1%** - 立即告警
- **响应时间超过3秒** - 警告告警
- **可用性低于99%** - 紧急告警
- **数据库连接失败** - 立即告警
