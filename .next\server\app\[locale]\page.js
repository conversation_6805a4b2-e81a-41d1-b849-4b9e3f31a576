/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/page";
exports.ids = ["app/[locale]/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./es.json": [
		"(rsc)/./messages/es.json",
		"_rsc_messages_es_json"
	],
	"./hi.json": [
		"(rsc)/./messages/hi.json",
		"_rsc_messages_hi_json"
	],
	"./ja.json": [
		"(rsc)/./messages/ja.json",
		"_rsc_messages_ja_json"
	],
	"./pt.json": [
		"(rsc)/./messages/pt.json",
		"_rsc_messages_pt_json"
	],
	"./zh.json": [
		"(rsc)/./messages/zh.json",
		"_rsc_messages_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(ssr)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(ssr)/./messages/en.json",
		"_ssr_messages_en_json"
	],
	"./es.json": [
		"(ssr)/./messages/es.json",
		"_ssr_messages_es_json"
	],
	"./hi.json": [
		"(ssr)/./messages/hi.json",
		"_ssr_messages_hi_json"
	],
	"./ja.json": [
		"(ssr)/./messages/ja.json",
		"_ssr_messages_ja_json"
	],
	"./pt.json": [
		"(ssr)/./messages/pt.json",
		"_ssr_messages_pt_json"
	],
	"./zh.json": [
		"(ssr)/./messages/zh.json",
		"_ssr_messages_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(ssr)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/page.tsx */ \"(rsc)/./src/app/[locale]/page.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/page\",\n        pathname: \"/[locale]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira-code%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22firaCode%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-playfair%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Fira_Code%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-fira-code%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22firaCode%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/header.tsx */ \"(ssr)/./src/components/layout/header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUN0YXJvdC1zZW8lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Rhcm90LXNlbyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Rhcm90LXNlbyU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNoZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySGVhZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnUEFBc0o7QUFDdEo7QUFDQSxnTUFBNkY7QUFDN0Y7QUFDQSxnTEFBbUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teXN0aWNhbC13ZWJzaXRlLz9iMTE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkU6XFxcXHRhcm90LXNlb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXHNoYXJlZFxcXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFx0YXJvdC1zZW9cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiSGVhZGVyXCJdICovIFwiRTpcXFxcdGFyb3Qtc2VvXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxoZWFkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUN0YXJvdC1zZW8lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0LWludGwlNUMlNUNkaXN0JTVDJTVDZXNtJTVDJTVDc2hhcmVkJTVDJTVDTmV4dEludGxDbGllbnRQcm92aWRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdQQUFzSiIsInNvdXJjZXMiOlsid2VicGFjazovL215c3RpY2FsLXdlYnNpdGUvPzRhNGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRTpcXFxcdGFyb3Qtc2VvXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcc2hhcmVkXFxcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Ctarot-seo%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./navigation */ \"(ssr)/./src/components/layout/navigation.tsx\");\n/* harmony import */ var _language_switcher__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./language-switcher */ \"(ssr)/./src/components/layout/language-switcher.tsx\");\n/* harmony import */ var _theme_toggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./theme-toggle */ \"(ssr)/./src/components/layout/theme-toggle.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Header,default auto */ \n\n\n\n\n\n\n\n\nfunction Header({ locale, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)();\n    const [isScrolled, setIsScrolled] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const logoHref = locale === \"en\" ? \"/\" : `/${locale}`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"sticky top-0 z-50 w-full transition-all duration-300\", isScrolled ? \"bg-background/80 backdrop-blur-md border-b border-border/50 shadow-sm\" : \"bg-transparent\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: logoHref,\n                            className: \"flex items-center space-x-2 hover:opacity-80 transition-opacity\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-br from-mystical-500 to-cosmic-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-lg font-bold\",\n                                        children: \"M\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold bg-gradient-to-r from-mystical-600 to-cosmic-600 bg-clip-text text-transparent\",\n                                        children: locale === \"zh\" ? \"神秘洞察\" : locale === \"es\" ? \"Perspectivas M\\xedsticas\" : locale === \"pt\" ? \"Percep\\xe7\\xf5es M\\xedsticas\" : locale === \"hi\" ? \"रहस्यमय अंतर्दृष्टि\" : locale === \"ja\" ? \"ミスティカル・インサイト\" : \"Mystical Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navigation__WEBPACK_IMPORTED_MODULE_3__.Navigation, {\n                            locale: locale\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_language_switcher__WEBPACK_IMPORTED_MODULE_4__.LanguageSwitcher, {\n                                currentLocale: locale\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_theme_toggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"mystical\",\n                                    size: \"sm\",\n                                    children: t(\"hero.cta\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDC64\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\header.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFK0I7QUFDRjtBQUNlO0FBQ0Y7QUFDYTtBQUNWO0FBQ0c7QUFDZjtBQVExQixTQUFTUSxPQUFPLEVBQUVDLE1BQU0sRUFBRUMsU0FBUyxFQUFlO0lBQ3ZELE1BQU1DLElBQUlULDBEQUFlQTtJQUN6QixNQUFNLENBQUNVLFlBQVlDLGNBQWMsR0FBR2IsMkNBQWMsQ0FBQztJQUVuREEsNENBQWUsQ0FBQztRQUNkLE1BQU1nQixlQUFlO1lBQ25CSCxjQUFjSSxPQUFPQyxPQUFPLEdBQUc7UUFDakM7UUFFQUQsT0FBT0UsZ0JBQWdCLENBQUMsVUFBVUg7UUFDbEMsT0FBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjtJQUNwRCxHQUFHLEVBQUU7SUFFTCxNQUFNSyxXQUFXWixXQUFXLE9BQU8sTUFBTSxDQUFDLENBQUMsRUFBRUEsT0FBTyxDQUFDO0lBRXJELHFCQUNFLDhEQUFDYTtRQUNDWixXQUFXSCw4Q0FBRUEsQ0FDWCx3REFDQUssYUFDSSwwRUFDQSxrQkFDSkY7a0JBR0YsNEVBQUNhO1lBQUliLFdBQVU7c0JBQ2IsNEVBQUNhO2dCQUFJYixXQUFVOztrQ0FFYiw4REFBQ2E7d0JBQUliLFdBQVU7a0NBQ2IsNEVBQUNULGlEQUFJQTs0QkFDSHVCLE1BQU1IOzRCQUNOWCxXQUFVOzs4Q0FFViw4REFBQ2E7b0NBQUliLFdBQVU7OENBQ2IsNEVBQUNlO3dDQUFLZixXQUFVO2tEQUErQjs7Ozs7Ozs7Ozs7OENBRWpELDhEQUFDYTtvQ0FBSWIsV0FBVTs4Q0FDYiw0RUFBQ2dCO3dDQUFHaEIsV0FBVTtrREFDWEQsV0FBVyxPQUFPLFNBQ2xCQSxXQUFXLE9BQU8sNkJBQ2xCQSxXQUFXLE9BQU8saUNBQ2xCQSxXQUFXLE9BQU8sd0JBQ2xCQSxXQUFXLE9BQU8saUJBQ2xCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9ULDhEQUFDYzt3QkFBSWIsV0FBVTtrQ0FDYiw0RUFBQ1AsbURBQVVBOzRCQUFDTSxRQUFRQTs7Ozs7Ozs7Ozs7a0NBSXRCLDhEQUFDYzt3QkFBSWIsV0FBVTs7MENBRWIsOERBQUNOLGdFQUFnQkE7Z0NBQUN1QixlQUFlbEI7Ozs7OzswQ0FHakMsOERBQUNKLHNEQUFXQTs7Ozs7MENBR1osOERBQUNrQjtnQ0FBSWIsV0FBVTswQ0FDYiw0RUFBQ0oseURBQU1BO29DQUFDc0IsU0FBUTtvQ0FBV0MsTUFBSzs4Q0FDN0JsQixFQUFFOzs7Ozs7Ozs7OzswQ0FLUCw4REFBQ1k7Z0NBQUliLFdBQVU7MENBQ2IsNEVBQUNKLHlEQUFNQTtvQ0FBQ3NCLFNBQVE7b0NBQVFDLE1BQUs7OENBQzNCLDRFQUFDSjt3Q0FBS2YsV0FBVTtrREFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRMUM7QUFFQSxpRUFBZUYsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL215c3RpY2FsLXdlYnNpdGUvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvaGVhZGVyLnRzeD80YWMyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9ucyB9IGZyb20gJ25leHQtaW50bCc7XG5pbXBvcnQgeyBOYXZpZ2F0aW9uIH0gZnJvbSAnLi9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IExhbmd1YWdlU3dpdGNoZXIgfSBmcm9tICcuL2xhbmd1YWdlLXN3aXRjaGVyJztcbmltcG9ydCB7IFRoZW1lVG9nZ2xlIH0gZnJvbSAnLi90aGVtZS10b2dnbGUnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcbmltcG9ydCB0eXBlIHsgTG9jYWxlIH0gZnJvbSAnQC90eXBlcyc7XG5cbmludGVyZmFjZSBIZWFkZXJQcm9wcyB7XG4gIGxvY2FsZTogTG9jYWxlO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBIZWFkZXIoeyBsb2NhbGUsIGNsYXNzTmFtZSB9OiBIZWFkZXJQcm9wcykge1xuICBjb25zdCB0ID0gdXNlVHJhbnNsYXRpb25zKCk7XG4gIGNvbnN0IFtpc1Njcm9sbGVkLCBzZXRJc1Njcm9sbGVkXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZVNjcm9sbCA9ICgpID0+IHtcbiAgICAgIHNldElzU2Nyb2xsZWQod2luZG93LnNjcm9sbFkgPiAxMCk7XG4gICAgfTtcblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpO1xuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgaGFuZGxlU2Nyb2xsKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGxvZ29IcmVmID0gbG9jYWxlID09PSAnZW4nID8gJy8nIDogYC8ke2xvY2FsZX1gO1xuXG4gIHJldHVybiAoXG4gICAgPGhlYWRlclxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgJ3N0aWNreSB0b3AtMCB6LTUwIHctZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAnLFxuICAgICAgICBpc1Njcm9sbGVkXG4gICAgICAgICAgPyAnYmctYmFja2dyb3VuZC84MCBiYWNrZHJvcC1ibHVyLW1kIGJvcmRlci1iIGJvcmRlci1ib3JkZXIvNTAgc2hhZG93LXNtJ1xuICAgICAgICAgIDogJ2JnLXRyYW5zcGFyZW50JyxcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC0xNiBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgey8qIExvZ28gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9e2xvZ29IcmVmfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgaG92ZXI6b3BhY2l0eS04MCB0cmFuc2l0aW9uLW9wYWNpdHlcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1teXN0aWNhbC01MDAgdG8tY29zbWljLTYwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LWxnIGZvbnQtYm9sZFwiPk08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBzbTpibG9ja1wiPlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tbXlzdGljYWwtNjAwIHRvLWNvc21pYy02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgICAgICAgIHtsb2NhbGUgPT09ICd6aCcgPyAn56We56eY5rSe5a+fJyA6IFxuICAgICAgICAgICAgICAgICAgIGxvY2FsZSA9PT0gJ2VzJyA/ICdQZXJzcGVjdGl2YXMgTcOtc3RpY2FzJyA6XG4gICAgICAgICAgICAgICAgICAgbG9jYWxlID09PSAncHQnID8gJ1BlcmNlcMOnw7VlcyBNw61zdGljYXMnIDpcbiAgICAgICAgICAgICAgICAgICBsb2NhbGUgPT09ICdoaScgPyAn4KSw4KS54KS44KWN4KSv4KSu4KSvIOCkheCkguCkpOCksOCljeCkpuClg+Ckt+CljeCkn+CkvycgOlxuICAgICAgICAgICAgICAgICAgIGxvY2FsZSA9PT0gJ2phJyA/ICfjg5/jgrnjg4bjgqPjgqvjg6vjg7vjgqTjg7PjgrXjgqTjg4gnIDpcbiAgICAgICAgICAgICAgICAgICAnTXlzdGljYWwgSW5zaWdodHMnfVxuICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPE5hdmlnYXRpb24gbG9jYWxlPXtsb2NhbGV9IC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUmlnaHQgU2lkZSBBY3Rpb25zICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICB7LyogTGFuZ3VhZ2UgU3dpdGNoZXIgKi99XG4gICAgICAgICAgICA8TGFuZ3VhZ2VTd2l0Y2hlciBjdXJyZW50TG9jYWxlPXtsb2NhbGV9IC8+XG5cbiAgICAgICAgICAgIHsvKiBUaGVtZSBUb2dnbGUgKi99XG4gICAgICAgICAgICA8VGhlbWVUb2dnbGUgLz5cblxuICAgICAgICAgICAgey8qIENUQSBCdXR0b24gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBzbTpibG9ja1wiPlxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJteXN0aWNhbFwiIHNpemU9XCJzbVwiPlxuICAgICAgICAgICAgICAgIHt0KCdoZXJvLmN0YScpfVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogVXNlciBNZW51IChwbGFjZWhvbGRlciBmb3IgZnV0dXJlIGltcGxlbWVudGF0aW9uKSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmJsb2NrXCI+XG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cImljb25cIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+8J+RpDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2hlYWRlcj5cbiAgKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgSGVhZGVyO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGluayIsInVzZVRyYW5zbGF0aW9ucyIsIk5hdmlnYXRpb24iLCJMYW5ndWFnZVN3aXRjaGVyIiwiVGhlbWVUb2dnbGUiLCJCdXR0b24iLCJjbiIsIkhlYWRlciIsImxvY2FsZSIsImNsYXNzTmFtZSIsInQiLCJpc1Njcm9sbGVkIiwic2V0SXNTY3JvbGxlZCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiaGFuZGxlU2Nyb2xsIiwid2luZG93Iiwic2Nyb2xsWSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwibG9nb0hyZWYiLCJoZWFkZXIiLCJkaXYiLCJocmVmIiwic3BhbiIsImgxIiwiY3VycmVudExvY2FsZSIsInZhcmlhbnQiLCJzaXplIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/language-switcher.tsx":
/*!*****************************************************!*\
  !*** ./src/components/layout/language-switcher.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageSwitcher: () => (/* binding */ LanguageSwitcher),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/i18n */ \"(ssr)/./src/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageSwitcher,default auto */ \n\n\n\n\n\n\n\n\nfunction LanguageSwitcher({ currentLocale, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"common\");\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isOpen, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // 获取当前路径（去除语言前缀）\n    const getPathWithoutLocale = ()=>{\n        const segments = pathname.split(\"/\").filter(Boolean);\n        if (segments.length > 0 && _i18n__WEBPACK_IMPORTED_MODULE_7__.locales.includes(segments[0])) {\n            return \"/\" + segments.slice(1).join(\"/\");\n        }\n        return pathname;\n    };\n    // 生成新语言的URL\n    const getLocalizedPath = (locale)=>{\n        const pathWithoutLocale = getPathWithoutLocale();\n        return locale === \"en\" ? pathWithoutLocale || \"/\" : `/${locale}${pathWithoutLocale}`;\n    };\n    const currentConfig = _i18n__WEBPACK_IMPORTED_MODULE_7__.localeConfig[currentLocale];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2\",\n                \"aria-label\": t(\"language\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-base\",\n                        children: currentConfig.flag\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hidden sm:inline text-sm font-medium\",\n                        children: currentConfig.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-4 h-4 transition-transform duration-200\", isOpen ? \"rotate-180\" : \"rotate-0\"),\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-full right-0 mt-2 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            variant: \"mystical\",\n                            className: \"p-2 min-w-[200px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-3 py-2 text-xs font-medium text-muted-foreground uppercase tracking-wider\",\n                                        children: t(\"language\")\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 17\n                                    }, this),\n                                    _i18n__WEBPACK_IMPORTED_MODULE_7__.locales.map((locale)=>{\n                                        const config = _i18n__WEBPACK_IMPORTED_MODULE_7__.localeConfig[locale];\n                                        const isActive = locale === currentLocale;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: getLocalizedPath(locale),\n                                            onClick: ()=>setIsOpen(false),\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"flex items-center space-x-3 px-3 py-2 rounded-md text-sm transition-colors\", \"hover:bg-accent hover:text-accent-foreground\", \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", isActive ? \"bg-primary text-primary-foreground\" : \"text-foreground\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-base\",\n                                                    children: config.flag\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: config.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: config.region\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 23\n                                                }, this),\n                                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 text-primary-foreground\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, locale, true, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40\",\n                        onClick: ()=>setIsOpen(false)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\language-switcher.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/language-switcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ Navigation,default auto */ \n\n\n\n\n\n\n\nconst navigationItems = [\n    {\n        key: \"home\",\n        href: \"/\",\n        icon: \"\\uD83C\\uDFE0\"\n    },\n    {\n        key: \"blog\",\n        href: \"/blog\",\n        icon: \"\\uD83D\\uDCDD\"\n    },\n    {\n        key: \"tarot\",\n        href: \"/tarot\",\n        icon: \"\\uD83D\\uDD2E\"\n    },\n    {\n        key: \"astrology\",\n        href: \"/astrology\",\n        icon: \"⭐\"\n    },\n    {\n        key: \"numerology\",\n        href: \"/numerology\",\n        icon: \"\\uD83D\\uDD22\"\n    },\n    {\n        key: \"tests\",\n        href: \"/tests\",\n        icon: \"\\uD83E\\uDDEA\"\n    },\n    {\n        key: \"products\",\n        href: \"/products\",\n        icon: \"\\uD83D\\uDECD️\"\n    }\n];\nfunction Navigation({ locale, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"navigation\");\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // 获取当前路径（去除语言前缀）\n    const currentPath = pathname.replace(`/${locale}`, \"\") || \"/\";\n    const getHref = (href)=>{\n        return locale === \"en\" ? href : `/${locale}${href}`;\n    };\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return currentPath === \"/\";\n        }\n        return currentPath.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex items-center space-x-1\",\n                children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: getHref(item.href),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\", \"hover:bg-accent hover:text-accent-foreground\", \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", isActive(item.href) ? \"bg-primary text-primary-foreground shadow-sm\" : \"text-muted-foreground hover:text-foreground\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: item.icon\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            t(item.key)\n                        ]\n                    }, item.key, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                    className: \"relative\",\n                    \"aria-label\": \"Toggle navigation menu\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center w-6 h-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"block h-0.5 w-6 bg-current transition-all duration-300\", isMobileMenuOpen ? \"rotate-45 translate-y-1\" : \"-translate-y-1\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"block h-0.5 w-6 bg-current transition-all duration-300\", isMobileMenuOpen ? \"opacity-0\" : \"opacity-100\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"block h-0.5 w-6 bg-current transition-all duration-300\", isMobileMenuOpen ? \"-rotate-45 -translate-y-1\" : \"translate-y-1\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-full left-0 right-0 mt-2 md:hidden z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    variant: \"mystical\",\n                    className: \"p-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-1\",\n                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: getHref(item.href),\n                                onClick: ()=>setIsMobileMenuOpen(false),\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200\", \"hover:bg-accent hover:text-accent-foreground\", \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", isActive(item.href) ? \"bg-primary text-primary-foreground shadow-sm\" : \"text-muted-foreground hover:text-foreground\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-3 text-lg\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 19\n                                    }, this),\n                                    t(item.key)\n                                ]\n                            }, item.key, true, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/20 backdrop-blur-sm z-40 md:hidden\",\n                onClick: ()=>setIsMobileMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\navigation.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/theme-toggle.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/theme-toggle.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle,default auto */ \n\n\n\n\nfunction ThemeToggle({ className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)(\"common\");\n    const [theme, setTheme] = react__WEBPACK_IMPORTED_MODULE_1__.useState(\"system\");\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        setMounted(true);\n        const savedTheme = localStorage.getItem(\"theme\");\n        if (savedTheme) {\n            setTheme(savedTheme);\n        }\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!mounted) return;\n        const root = window.document.documentElement;\n        root.classList.remove(\"light\", \"dark\");\n        if (theme === \"system\") {\n            const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n            root.classList.add(systemTheme);\n        } else {\n            root.classList.add(theme);\n        }\n        localStorage.setItem(\"theme\", theme);\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        const themes = [\n            \"light\",\n            \"dark\",\n            \"system\"\n        ];\n        const currentIndex = themes.indexOf(theme);\n        const nextIndex = (currentIndex + 1) % themes.length;\n        setTheme(themes[nextIndex]);\n    };\n    const getThemeIcon = ()=>{\n        switch(theme){\n            case \"light\":\n                return \"☀️\";\n            case \"dark\":\n                return \"\\uD83C\\uDF19\";\n            case \"system\":\n                return \"\\uD83D\\uDCBB\";\n            default:\n                return \"\\uD83D\\uDCBB\";\n        }\n    };\n    const getThemeLabel = ()=>{\n        switch(theme){\n            case \"light\":\n                return t(\"light\");\n            case \"dark\":\n                return t(\"dark\");\n            case \"system\":\n                return t(\"system\");\n            default:\n                return t(\"system\");\n        }\n    };\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            variant: \"ghost\",\n            size: \"icon\",\n            disabled: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-lg\",\n                children: \"\\uD83D\\uDCBB\"\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\theme-toggle.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\theme-toggle.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onClick: toggleTheme,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"transition-all duration-200\", className),\n        title: `${t(\"theme\")}: ${getThemeLabel()}`,\n        \"aria-label\": `${t(\"theme\")}: ${getThemeLabel()}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-lg\",\n            children: getThemeIcon()\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\theme-toggle.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\theme-toggle.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeToggle);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvdGhlbWUtdG9nZ2xlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUUrQjtBQUNhO0FBQ0k7QUFDZjtBQUkxQixTQUFTSSxZQUFZLEVBQUVDLFNBQVMsRUFBMEI7SUFDL0QsTUFBTUMsSUFBSUwsMERBQWVBLENBQUM7SUFDMUIsTUFBTSxDQUFDTSxPQUFPQyxTQUFTLEdBQUdSLDJDQUFjLENBQVE7SUFDaEQsTUFBTSxDQUFDVSxTQUFTQyxXQUFXLEdBQUdYLDJDQUFjLENBQUM7SUFFN0NBLDRDQUFlLENBQUM7UUFDZFcsV0FBVztRQUNYLE1BQU1FLGFBQWFDLGFBQWFDLE9BQU8sQ0FBQztRQUN4QyxJQUFJRixZQUFZO1lBQ2RMLFNBQVNLO1FBQ1g7SUFDRixHQUFHLEVBQUU7SUFFTGIsNENBQWUsQ0FBQztRQUNkLElBQUksQ0FBQ1UsU0FBUztRQUVkLE1BQU1NLE9BQU9DLE9BQU9DLFFBQVEsQ0FBQ0MsZUFBZTtRQUM1Q0gsS0FBS0ksU0FBUyxDQUFDQyxNQUFNLENBQUMsU0FBUztRQUUvQixJQUFJZCxVQUFVLFVBQVU7WUFDdEIsTUFBTWUsY0FBY0wsT0FBT00sVUFBVSxDQUFDLGdDQUFnQ0MsT0FBTyxHQUN6RSxTQUNBO1lBQ0pSLEtBQUtJLFNBQVMsQ0FBQ0ssR0FBRyxDQUFDSDtRQUNyQixPQUFPO1lBQ0xOLEtBQUtJLFNBQVMsQ0FBQ0ssR0FBRyxDQUFDbEI7UUFDckI7UUFFQU8sYUFBYVksT0FBTyxDQUFDLFNBQVNuQjtJQUNoQyxHQUFHO1FBQUNBO1FBQU9HO0tBQVE7SUFFbkIsTUFBTWlCLGNBQWM7UUFDbEIsTUFBTUMsU0FBa0I7WUFBQztZQUFTO1lBQVE7U0FBUztRQUNuRCxNQUFNQyxlQUFlRCxPQUFPRSxPQUFPLENBQUN2QjtRQUNwQyxNQUFNd0IsWUFBWSxDQUFDRixlQUFlLEtBQUtELE9BQU9JLE1BQU07UUFDcER4QixTQUFTb0IsTUFBTSxDQUFDRyxVQUFVO0lBQzVCO0lBRUEsTUFBTUUsZUFBZTtRQUNuQixPQUFRMUI7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU0yQixnQkFBZ0I7UUFDcEIsT0FBUTNCO1lBQ04sS0FBSztnQkFDSCxPQUFPRCxFQUFFO1lBQ1gsS0FBSztnQkFDSCxPQUFPQSxFQUFFO1lBQ1gsS0FBSztnQkFDSCxPQUFPQSxFQUFFO1lBQ1g7Z0JBQ0UsT0FBT0EsRUFBRTtRQUNiO0lBQ0Y7SUFFQSxJQUFJLENBQUNJLFNBQVM7UUFDWixxQkFDRSw4REFBQ1IseURBQU1BO1lBQUNpQyxTQUFRO1lBQVFDLE1BQUs7WUFBT0MsUUFBUTtzQkFDMUMsNEVBQUNDO2dCQUFLakMsV0FBVTswQkFBVTs7Ozs7Ozs7Ozs7SUFHaEM7SUFFQSxxQkFDRSw4REFBQ0gseURBQU1BO1FBQ0xpQyxTQUFRO1FBQ1JDLE1BQUs7UUFDTEcsU0FBU1o7UUFDVHRCLFdBQVdGLDhDQUFFQSxDQUFDLCtCQUErQkU7UUFDN0NtQyxPQUFPLENBQUMsRUFBRWxDLEVBQUUsU0FBUyxFQUFFLEVBQUU0QixnQkFBZ0IsQ0FBQztRQUMxQ08sY0FBWSxDQUFDLEVBQUVuQyxFQUFFLFNBQVMsRUFBRSxFQUFFNEIsZ0JBQWdCLENBQUM7a0JBRS9DLDRFQUFDSTtZQUFLakMsV0FBVTtzQkFBVzRCOzs7Ozs7Ozs7OztBQUdqQztBQUVBLGlFQUFlN0IsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL215c3RpY2FsLXdlYnNpdGUvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvdGhlbWUtdG9nZ2xlLnRzeD9mNjcxIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSAnbmV4dC1pbnRsJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbnR5cGUgVGhlbWUgPSAnbGlnaHQnIHwgJ2RhcmsnIHwgJ3N5c3RlbSc7XG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVRvZ2dsZSh7IGNsYXNzTmFtZSB9OiB7IGNsYXNzTmFtZT86IHN0cmluZyB9KSB7XG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoJ2NvbW1vbicpO1xuICBjb25zdCBbdGhlbWUsIHNldFRoZW1lXSA9IFJlYWN0LnVzZVN0YXRlPFRoZW1lPignc3lzdGVtJyk7XG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldE1vdW50ZWQodHJ1ZSk7XG4gICAgY29uc3Qgc2F2ZWRUaGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0aGVtZScpIGFzIFRoZW1lO1xuICAgIGlmIChzYXZlZFRoZW1lKSB7XG4gICAgICBzZXRUaGVtZShzYXZlZFRoZW1lKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghbW91bnRlZCkgcmV0dXJuO1xuXG4gICAgY29uc3Qgcm9vdCA9IHdpbmRvdy5kb2N1bWVudC5kb2N1bWVudEVsZW1lbnQ7XG4gICAgcm9vdC5jbGFzc0xpc3QucmVtb3ZlKCdsaWdodCcsICdkYXJrJyk7XG5cbiAgICBpZiAodGhlbWUgPT09ICdzeXN0ZW0nKSB7XG4gICAgICBjb25zdCBzeXN0ZW1UaGVtZSA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlc1xuICAgICAgICA/ICdkYXJrJ1xuICAgICAgICA6ICdsaWdodCc7XG4gICAgICByb290LmNsYXNzTGlzdC5hZGQoc3lzdGVtVGhlbWUpO1xuICAgIH0gZWxzZSB7XG4gICAgICByb290LmNsYXNzTGlzdC5hZGQodGhlbWUpO1xuICAgIH1cblxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd0aGVtZScsIHRoZW1lKTtcbiAgfSwgW3RoZW1lLCBtb3VudGVkXSk7XG5cbiAgY29uc3QgdG9nZ2xlVGhlbWUgPSAoKSA9PiB7XG4gICAgY29uc3QgdGhlbWVzOiBUaGVtZVtdID0gWydsaWdodCcsICdkYXJrJywgJ3N5c3RlbSddO1xuICAgIGNvbnN0IGN1cnJlbnRJbmRleCA9IHRoZW1lcy5pbmRleE9mKHRoZW1lKTtcbiAgICBjb25zdCBuZXh0SW5kZXggPSAoY3VycmVudEluZGV4ICsgMSkgJSB0aGVtZXMubGVuZ3RoO1xuICAgIHNldFRoZW1lKHRoZW1lc1tuZXh0SW5kZXhdKTtcbiAgfTtcblxuICBjb25zdCBnZXRUaGVtZUljb24gPSAoKSA9PiB7XG4gICAgc3dpdGNoICh0aGVtZSkge1xuICAgICAgY2FzZSAnbGlnaHQnOlxuICAgICAgICByZXR1cm4gJ+KYgO+4jyc7XG4gICAgICBjYXNlICdkYXJrJzpcbiAgICAgICAgcmV0dXJuICfwn4yZJztcbiAgICAgIGNhc2UgJ3N5c3RlbSc6XG4gICAgICAgIHJldHVybiAn8J+Suyc7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gJ/CfkrsnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRUaGVtZUxhYmVsID0gKCkgPT4ge1xuICAgIHN3aXRjaCAodGhlbWUpIHtcbiAgICAgIGNhc2UgJ2xpZ2h0JzpcbiAgICAgICAgcmV0dXJuIHQoJ2xpZ2h0Jyk7XG4gICAgICBjYXNlICdkYXJrJzpcbiAgICAgICAgcmV0dXJuIHQoJ2RhcmsnKTtcbiAgICAgIGNhc2UgJ3N5c3RlbSc6XG4gICAgICAgIHJldHVybiB0KCdzeXN0ZW0nKTtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiB0KCdzeXN0ZW0nKTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKCFtb3VudGVkKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cImljb25cIiBkaXNhYmxlZD5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZ1wiPvCfkrs8L3NwYW4+XG4gICAgICA8L0J1dHRvbj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8QnV0dG9uXG4gICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgc2l6ZT1cImljb25cIlxuICAgICAgb25DbGljaz17dG9nZ2xlVGhlbWV9XG4gICAgICBjbGFzc05hbWU9e2NuKCd0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAnLCBjbGFzc05hbWUpfVxuICAgICAgdGl0bGU9e2Ake3QoJ3RoZW1lJyl9OiAke2dldFRoZW1lTGFiZWwoKX1gfVxuICAgICAgYXJpYS1sYWJlbD17YCR7dCgndGhlbWUnKX06ICR7Z2V0VGhlbWVMYWJlbCgpfWB9XG4gICAgPlxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZ1wiPntnZXRUaGVtZUljb24oKX08L3NwYW4+XG4gICAgPC9CdXR0b24+XG4gICk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IFRoZW1lVG9nZ2xlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlVHJhbnNsYXRpb25zIiwiQnV0dG9uIiwiY24iLCJUaGVtZVRvZ2dsZSIsImNsYXNzTmFtZSIsInQiLCJ0aGVtZSIsInNldFRoZW1lIiwidXNlU3RhdGUiLCJtb3VudGVkIiwic2V0TW91bnRlZCIsInVzZUVmZmVjdCIsInNhdmVkVGhlbWUiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwicm9vdCIsIndpbmRvdyIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiY2xhc3NMaXN0IiwicmVtb3ZlIiwic3lzdGVtVGhlbWUiLCJtYXRjaE1lZGlhIiwibWF0Y2hlcyIsImFkZCIsInNldEl0ZW0iLCJ0b2dnbGVUaGVtZSIsInRoZW1lcyIsImN1cnJlbnRJbmRleCIsImluZGV4T2YiLCJuZXh0SW5kZXgiLCJsZW5ndGgiLCJnZXRUaGVtZUljb24iLCJnZXRUaGVtZUxhYmVsIiwidmFyaWFudCIsInNpemUiLCJkaXNhYmxlZCIsInNwYW4iLCJvbkNsaWNrIiwidGl0bGUiLCJhcmlhLWxhYmVsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/theme-toggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            mystical: \"bg-gradient-to-r from-mystical-600 to-cosmic-600 text-white hover:from-mystical-700 hover:to-cosmic-700 shadow-lg hover:shadow-xl transition-all duration-300\",\n            cosmic: \"bg-gradient-to-r from-cosmic-500 to-mystical-500 text-white hover:from-cosmic-600 hover:to-mystical-600 shadow-lg hover:shadow-xl transition-all duration-300\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            xl: \"h-12 rounded-lg px-10 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 57,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 50,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDYTtBQUNzQjtBQUNqQztBQUVqQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwwUkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsVUFBVTtZQUNWQyxRQUFRO1FBQ1Y7UUFDQUMsTUFBTTtZQUNKUixTQUFTO1lBQ1RTLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmZCxTQUFTO1FBQ1RTLE1BQU07SUFDUjtBQUNGO0FBVUYsTUFBTU0sdUJBQVNyQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFdUIsU0FBUyxFQUFFakIsT0FBTyxFQUFFUyxJQUFJLEVBQUVTLFVBQVUsS0FBSyxFQUFFQyxVQUFVLEtBQUssRUFBRUMsUUFBUSxFQUFFQyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUM3RixNQUFNQyxPQUFPTixVQUFVdkIsc0RBQUlBLEdBQUc7SUFFOUIscUJBQ0UsOERBQUM2QjtRQUNDUCxXQUFXcEIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU1M7WUFBTVE7UUFBVTtRQUN4RE0sS0FBS0E7UUFDTEYsVUFBVUEsWUFBWUY7UUFDckIsR0FBR0csS0FBSzs7WUFFUkgseUJBQ0MsOERBQUNNO2dCQUNDUixXQUFVO2dCQUNWUyxPQUFNO2dCQUNOQyxNQUFLO2dCQUNMQyxTQUFROztrQ0FFUiw4REFBQ0M7d0JBQ0NaLFdBQVU7d0JBQ1ZhLElBQUc7d0JBQ0hDLElBQUc7d0JBQ0hDLEdBQUU7d0JBQ0ZDLFFBQU87d0JBQ1BDLGFBQVk7Ozs7OztrQ0FFZCw4REFBQ0M7d0JBQ0NsQixXQUFVO3dCQUNWVSxNQUFLO3dCQUNMUyxHQUFFOzs7Ozs7Ozs7Ozs7WUFJUGhCOzs7Ozs7O0FBR1A7QUFFRkwsT0FBT3NCLFdBQVcsR0FBRztBQUVhIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL3VpL2J1dHRvbi50c3g/NmEwYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTbG90IH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXNsb3QnO1xuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gJ2NsYXNzLXZhcmlhbmNlLWF1dGhvcml0eSc7XG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gICdpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6b3BhY2l0eS01MCcsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OiAnYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1wcmltYXJ5LzkwJyxcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XG4gICAgICAgICAgJ2JnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBob3ZlcjpiZy1kZXN0cnVjdGl2ZS85MCcsXG4gICAgICAgIG91dGxpbmU6XG4gICAgICAgICAgJ2JvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCcsXG4gICAgICAgIHNlY29uZGFyeTpcbiAgICAgICAgICAnYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgaG92ZXI6Ymctc2Vjb25kYXJ5LzgwJyxcbiAgICAgICAgZ2hvc3Q6ICdob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCcsXG4gICAgICAgIGxpbms6ICd0ZXh0LXByaW1hcnkgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZScsXG4gICAgICAgIG15c3RpY2FsOiAnYmctZ3JhZGllbnQtdG8tciBmcm9tLW15c3RpY2FsLTYwMCB0by1jb3NtaWMtNjAwIHRleHQtd2hpdGUgaG92ZXI6ZnJvbS1teXN0aWNhbC03MDAgaG92ZXI6dG8tY29zbWljLTcwMCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCcsXG4gICAgICAgIGNvc21pYzogJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1jb3NtaWMtNTAwIHRvLW15c3RpY2FsLTUwMCB0ZXh0LXdoaXRlIGhvdmVyOmZyb20tY29zbWljLTYwMCBob3Zlcjp0by1teXN0aWNhbC02MDAgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAnLFxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgZGVmYXVsdDogJ2gtMTAgcHgtNCBweS0yJyxcbiAgICAgICAgc206ICdoLTkgcm91bmRlZC1tZCBweC0zJyxcbiAgICAgICAgbGc6ICdoLTExIHJvdW5kZWQtbWQgcHgtOCcsXG4gICAgICAgIHhsOiAnaC0xMiByb3VuZGVkLWxnIHB4LTEwIHRleHQtYmFzZScsXG4gICAgICAgIGljb246ICdoLTEwIHctMTAnLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogJ2RlZmF1bHQnLFxuICAgICAgc2l6ZTogJ2RlZmF1bHQnLFxuICAgIH0sXG4gIH1cbik7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xuICBhc0NoaWxkPzogYm9vbGVhbjtcbiAgbG9hZGluZz86IGJvb2xlYW47XG59XG5cbmNvbnN0IEJ1dHRvbiA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTEJ1dHRvbkVsZW1lbnQsIEJ1dHRvblByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB2YXJpYW50LCBzaXplLCBhc0NoaWxkID0gZmFsc2UsIGxvYWRpbmcgPSBmYWxzZSwgY2hpbGRyZW4sIGRpc2FibGVkLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiAnYnV0dG9uJztcbiAgICBcbiAgICByZXR1cm4gKFxuICAgICAgPENvbXBcbiAgICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWQgfHwgbG9hZGluZ31cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgPlxuICAgICAgICB7bG9hZGluZyAmJiAoXG4gICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00IGFuaW1hdGUtc3BpblwiXG4gICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgICAgIGZpbGw9XCJub25lXCJcbiAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxjaXJjbGVcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib3BhY2l0eS0yNVwiXG4gICAgICAgICAgICAgIGN4PVwiMTJcIlxuICAgICAgICAgICAgICBjeT1cIjEyXCJcbiAgICAgICAgICAgICAgcj1cIjEwXCJcbiAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCI0XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvcGFjaXR5LTc1XCJcbiAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgIGQ9XCJtNCAxMmE4IDggMCAwMTgtOFYwQzUuMzczIDAgMCA1LjM3MyAwIDEyaDR6bTIgNS4yOTFBNy45NjIgNy45NjIgMCAwMTQgMTJIMGMwIDMuMDQyIDEuMTM1IDUuODI0IDMgNy45MzhsMy0yLjY0N3pcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgKX1cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9Db21wPlxuICAgICk7XG4gIH1cbik7XG5CdXR0b24uZGlzcGxheU5hbWUgPSAnQnV0dG9uJztcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2xvdCIsImN2YSIsImNuIiwiYnV0dG9uVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJsaW5rIiwibXlzdGljYWwiLCJjb3NtaWMiLCJzaXplIiwic20iLCJsZyIsInhsIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJhc0NoaWxkIiwibG9hZGluZyIsImNoaWxkcmVuIiwiZGlzYWJsZWQiLCJwcm9wcyIsInJlZiIsIkNvbXAiLCJzdmciLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94IiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInBhdGgiLCJkIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", {\n    variants: {\n        variant: {\n            default: \"border-border\",\n            mystical: \"bg-card/50 backdrop-blur-sm border-border/50 shadow-lg hover:shadow-xl transition-all duration-300\",\n            cosmic: \"bg-gradient-to-br from-cosmic-50/50 to-mystical-50/50 border-cosmic-200/50 shadow-lg\",\n            glass: \"bg-card/30 backdrop-blur-md border-border/30 shadow-xl\"\n        },\n        padding: {\n            none: \"\",\n            sm: \"p-4\",\n            default: \"p-6\",\n            lg: \"p-8\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        padding: \"default\"\n    }\n});\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, padding, hover = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(cardVariants({\n            variant,\n            padding\n        }), hover && \"hover:-translate-y-1 transition-transform duration-300\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 101,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   detectLocaleFromHeaders: () => (/* binding */ detectLocaleFromHeaders),\n/* harmony export */   generateHreflangMap: () => (/* binding */ generateHreflangMap),\n/* harmony export */   getLocaleConfig: () => (/* binding */ getLocaleConfig),\n/* harmony export */   getLocaleFromPath: () => (/* binding */ getLocaleFromPath),\n/* harmony export */   getLocalePath: () => (/* binding */ getLocalePath),\n/* harmony export */   getLocalizedLanguageName: () => (/* binding */ getLocalizedLanguageName),\n/* harmony export */   isValidLocale: () => (/* binding */ isValidLocale),\n/* harmony export */   localeConfig: () => (/* binding */ localeConfig),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(ssr)/./node_modules/next-intl/dist/development/server.react-client.js\");\n\n\n// 支持的语言列表\nconst locales = [\n    \"en\",\n    \"zh\",\n    \"es\",\n    \"pt\",\n    \"hi\",\n    \"ja\"\n];\n// 默认语言\nconst defaultLocale = \"en\";\n// 语言配置映射\nconst localeConfig = {\n    en: {\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        dir: \"ltr\",\n        region: \"US\",\n        currency: \"USD\",\n        dateFormat: \"MM/dd/yyyy\"\n    },\n    zh: {\n        name: \"中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        dir: \"ltr\",\n        region: \"CN\",\n        currency: \"CNY\",\n        dateFormat: \"yyyy年MM月dd日\"\n    },\n    es: {\n        name: \"Espa\\xf1ol\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        dir: \"ltr\",\n        region: \"ES\",\n        currency: \"EUR\",\n        dateFormat: \"dd/MM/yyyy\"\n    },\n    pt: {\n        name: \"Portugu\\xeas\",\n        flag: \"\\uD83C\\uDDE7\\uD83C\\uDDF7\",\n        dir: \"ltr\",\n        region: \"BR\",\n        currency: \"BRL\",\n        dateFormat: \"dd/MM/yyyy\"\n    },\n    hi: {\n        name: \"हिन्दी\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\",\n        dir: \"ltr\",\n        region: \"IN\",\n        currency: \"INR\",\n        dateFormat: \"dd/MM/yyyy\"\n    },\n    ja: {\n        name: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        dir: \"ltr\",\n        region: \"JP\",\n        currency: \"JPY\",\n        dateFormat: \"yyyy年MM月dd日\"\n    }\n};\n// 验证语言是否支持\nfunction isValidLocale(locale) {\n    return locales.includes(locale);\n}\n// 获取语言配置\nfunction getLocaleConfig(locale) {\n    return localeConfig[locale];\n}\n// 获取语言的完整URL路径\nfunction getLocalePath(locale, path = \"\") {\n    const cleanPath = path.startsWith(\"/\") ? path.slice(1) : path;\n    return locale === defaultLocale ? `/${cleanPath}` : `/${locale}/${cleanPath}`;\n}\n// 从路径中提取语言\nfunction getLocaleFromPath(path) {\n    const segments = path.split(\"/\").filter(Boolean);\n    const firstSegment = segments[0];\n    if (firstSegment && isValidLocale(firstSegment)) {\n        return firstSegment;\n    }\n    return defaultLocale;\n}\n// 生成hreflang映射\nfunction generateHreflangMap(path, baseUrl) {\n    const hreflangMap = {};\n    locales.forEach((locale)=>{\n        const localePath = getLocalePath(locale, path);\n        hreflangMap[locale] = `${baseUrl}${localePath}`;\n    });\n    // 添加x-default\n    hreflangMap[\"x-default\"] = `${baseUrl}${getLocalePath(defaultLocale, path)}`;\n    return hreflangMap;\n}\n// next-intl配置\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__.getRequestConfig)(async ({ locale })=>{\n    // 验证传入的语言是否支持\n    if (!isValidLocale(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        return {\n            messages: (await __webpack_require__(\"(ssr)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default,\n            timeZone: getTimeZoneForLocale(locale),\n            now: new Date()\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 根据语言获取时区\nfunction getTimeZoneForLocale(locale) {\n    const timezoneMap = {\n        en: \"America/New_York\",\n        zh: \"Asia/Shanghai\",\n        es: \"Europe/Madrid\",\n        pt: \"America/Sao_Paulo\",\n        hi: \"Asia/Kolkata\",\n        ja: \"Asia/Tokyo\"\n    };\n    return timezoneMap[locale] || \"UTC\";\n}\n// 语言检测和重定向逻辑\nfunction detectLocaleFromHeaders(acceptLanguage) {\n    if (!acceptLanguage) return defaultLocale;\n    // 解析Accept-Language头\n    const languages = acceptLanguage.split(\",\").map((lang)=>{\n        const [code, q = \"1\"] = lang.trim().split(\";q=\");\n        return {\n            code: code.toLowerCase(),\n            quality: parseFloat(q)\n        };\n    }).sort((a, b)=>b.quality - a.quality);\n    // 查找匹配的语言\n    for (const { code } of languages){\n        // 精确匹配\n        if (isValidLocale(code)) {\n            return code;\n        }\n        // 语言代码匹配（如 zh-CN -> zh）\n        const langCode = code.split(\"-\")[0];\n        if (isValidLocale(langCode)) {\n            return langCode;\n        }\n    }\n    return defaultLocale;\n}\n// 获取语言的本地化名称\nfunction getLocalizedLanguageName(locale, targetLocale) {\n    const names = {\n        en: {\n            en: \"English\",\n            zh: \"Chinese\",\n            es: \"Spanish\",\n            pt: \"Portuguese\",\n            hi: \"Hindi\",\n            ja: \"Japanese\"\n        },\n        zh: {\n            en: \"英语\",\n            zh: \"中文\",\n            es: \"西班牙语\",\n            pt: \"葡萄牙语\",\n            hi: \"印地语\",\n            ja: \"日语\"\n        },\n        es: {\n            en: \"Ingl\\xe9s\",\n            zh: \"Chino\",\n            es: \"Espa\\xf1ol\",\n            pt: \"Portugu\\xe9s\",\n            hi: \"Hindi\",\n            ja: \"Japon\\xe9s\"\n        },\n        pt: {\n            en: \"Ingl\\xeas\",\n            zh: \"Chin\\xeas\",\n            es: \"Espanhol\",\n            pt: \"Portugu\\xeas\",\n            hi: \"Hindi\",\n            ja: \"Japon\\xeas\"\n        },\n        hi: {\n            en: \"अंग्रेजी\",\n            zh: \"चीनी\",\n            es: \"स्पेनिश\",\n            pt: \"पुर्तगाली\",\n            hi: \"हिन्दी\",\n            ja: \"जापानी\"\n        },\n        ja: {\n            en: \"英語\",\n            zh: \"中国語\",\n            es: \"スペイン語\",\n            pt: \"ポルトガル語\",\n            hi: \"ヒンディー語\",\n            ja: \"日本語\"\n        }\n    };\n    return names[targetLocale]?.[locale] || localeConfig[locale].name;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildUrl: () => (/* binding */ buildUrl),\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepMerge: () => (/* binding */ deepMerge),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getRandomElement: () => (/* binding */ getRandomElement),\n/* harmony export */   getUrlParams: () => (/* binding */ getUrlParams),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合并Tailwind CSS类名\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期\n */ function formatDate(date, locale = \"en\", options = {}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n        ...options\n    };\n    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);\n}\n/**\n * 格式化相对时间\n */ function formatRelativeTime(date, locale = \"en\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    const rtf = new Intl.RelativeTimeFormat(locale, {\n        numeric: \"auto\"\n    });\n    if (diffInSeconds < 60) {\n        return rtf.format(-diffInSeconds, \"second\");\n    } else if (diffInSeconds < 3600) {\n        return rtf.format(-Math.floor(diffInSeconds / 60), \"minute\");\n    } else if (diffInSeconds < 86400) {\n        return rtf.format(-Math.floor(diffInSeconds / 3600), \"hour\");\n    } else if (diffInSeconds < 2592000) {\n        return rtf.format(-Math.floor(diffInSeconds / 86400), \"day\");\n    } else if (diffInSeconds < 31536000) {\n        return rtf.format(-Math.floor(diffInSeconds / 2592000), \"month\");\n    } else {\n        return rtf.format(-Math.floor(diffInSeconds / 31536000), \"year\");\n    }\n}\n/**\n * 生成URL友好的slug\n */ function generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // 移除特殊字符\n    .replace(/[\\s_-]+/g, \"-\") // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, \"\"); // 移除开头和结尾的连字符\n}\n/**\n * 截断文本\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + \"...\";\n}\n/**\n * 计算阅读时间（分钟）\n */ function calculateReadingTime(text) {\n    const wordsPerMinute = 200; // 平均阅读速度\n    const words = text.trim().split(/\\s+/).length;\n    return Math.ceil(words / wordsPerMinute);\n}\n/**\n * 验证邮箱格式\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * 生成随机ID\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * 深度合并对象\n */ function deepMerge(target, source) {\n    const result = {\n        ...target\n    };\n    for(const key in source){\n        if (source[key] && typeof source[key] === \"object\" && !Array.isArray(source[key])) {\n            result[key] = deepMerge(result[key] || {}, source[key]);\n        } else {\n            result[key] = source[key];\n        }\n    }\n    return result;\n}\n/**\n * 防抖函数\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 格式化数字\n */ function formatNumber(num, locale = \"en\", options = {}) {\n    return new Intl.NumberFormat(locale, options).format(num);\n}\n/**\n * 格式化货币\n */ function formatCurrency(amount, currency = \"USD\", locale = \"en\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n/**\n * 获取随机数组元素\n */ function getRandomElement(array) {\n    return array[Math.floor(Math.random() * array.length)];\n}\n/**\n * 打乱数组\n */ function shuffleArray(array) {\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n}\n/**\n * 检查是否为移动设备\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * 获取设备类型\n */ function getDeviceType() {\n    if (true) return \"desktop\";\n    const width = window.innerWidth;\n    if (width < 768) return \"mobile\";\n    if (width < 1024) return \"tablet\";\n    return \"desktop\";\n}\n/**\n * 复制到剪贴板\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (error) {\n        console.error(\"Failed to copy to clipboard:\", error);\n        return false;\n    }\n}\n/**\n * 获取URL参数\n */ function getUrlParams(url) {\n    const params = new URLSearchParams(new URL(url).search);\n    const result = {};\n    for (const [key, value] of params.entries()){\n        result[key] = value;\n    }\n    return result;\n}\n/**\n * 构建URL\n */ function buildUrl(base, params) {\n    const url = new URL(base,  false ? 0 : \"http://localhost:3000\");\n    Object.entries(params).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            url.searchParams.set(key, String(value));\n        }\n    });\n    return url.toString();\n}\n/**\n * 安全的JSON解析\n */ function safeJsonParse(json, fallback) {\n    try {\n        return JSON.parse(json);\n    } catch  {\n        return fallback;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f5889c65c7c9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXlzdGljYWwtd2Vic2l0ZS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/MTA2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY1ODg5YzY1YzdjOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n */ \"(rsc)/./src/i18n.ts\");\n\n\n\n\n\nfunction generateStaticParams() {\n    return _i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function generateMetadata({ params: { locale } }) {\n    // 验证语言\n    if (!(0,_i18n__WEBPACK_IMPORTED_MODULE_2__.isValidLocale)(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    const localeConfig = (0,_i18n__WEBPACK_IMPORTED_MODULE_2__.getLocaleConfig)(locale);\n    const baseUrl = \"http://localhost:3000\" || 0;\n    // 生成hreflang映射\n    const hreflangMap = (0,_i18n__WEBPACK_IMPORTED_MODULE_2__.generateHreflangMap)(\"/\", baseUrl);\n    // 根据语言设置不同的元数据\n    const getLocalizedMetadata = (locale)=>{\n        switch(locale){\n            case \"zh\":\n                return {\n                    title: {\n                        template: \"%s | 神秘洞察\",\n                        default: \"神秘洞察 - 塔罗、占星与灵性指导\"\n                    },\n                    description: \"通过专业的塔罗占卜、占星洞察和灵性指导发现你的命运。通过我们全面的神秘服务探索宇宙的奥秘。\",\n                    keywords: [\n                        \"塔罗\",\n                        \"占星\",\n                        \"灵性\",\n                        \"神秘\",\n                        \"占卜\",\n                        \"星座\",\n                        \"数字命理\"\n                    ]\n                };\n            case \"es\":\n                return {\n                    title: {\n                        template: \"%s | Perspectivas M\\xedsticas\",\n                        default: \"Perspectivas M\\xedsticas - Tarot, Astrolog\\xeda y Gu\\xeda Espiritual\"\n                    },\n                    description: \"Descubre tu destino con lecturas profesionales de tarot, perspectivas astrol\\xf3gicas y gu\\xeda espiritual. Explora los misterios del universo con nuestros servicios m\\xedsticos integrales.\",\n                    keywords: [\n                        \"tarot\",\n                        \"astrolog\\xeda\",\n                        \"espiritualidad\",\n                        \"m\\xedstico\",\n                        \"adivinaci\\xf3n\",\n                        \"hor\\xf3scopo\",\n                        \"numerolog\\xeda\"\n                    ]\n                };\n            case \"pt\":\n                return {\n                    title: {\n                        template: \"%s | Percep\\xe7\\xf5es M\\xedsticas\",\n                        default: \"Percep\\xe7\\xf5es M\\xedsticas - Tar\\xf4, Astrologia e Orienta\\xe7\\xe3o Espiritual\"\n                    },\n                    description: \"Descubra seu destino com leituras profissionais de tar\\xf4, percep\\xe7\\xf5es astrol\\xf3gicas e orienta\\xe7\\xe3o espiritual. Explore os mist\\xe9rios do universo com nossos servi\\xe7os m\\xedsticos abrangentes.\",\n                    keywords: [\n                        \"tar\\xf4\",\n                        \"astrologia\",\n                        \"espiritualidade\",\n                        \"m\\xedstico\",\n                        \"adivinha\\xe7\\xe3o\",\n                        \"hor\\xf3scopo\",\n                        \"numerologia\"\n                    ]\n                };\n            case \"hi\":\n                return {\n                    title: {\n                        template: \"%s | रहस्यमय अंतर्दृष्टि\",\n                        default: \"रहस्यमय अंतर्दृष्टि - टैरो, ज्योतिष और आध्यात्मिक मार्गदर्शन\"\n                    },\n                    description: \"पेशेवर टैरो रीडिंग, ज्योतिषीय अंतर्दृष्टि और आध्यात्मिक मार्गदर्शन के साथ अपने भाग्य की खोज करें। हमारी व्यापक रहस्यमय सेवाओं के साथ ब्रह्मांड के रहस्यों का अन्वेषण करें।\",\n                    keywords: [\n                        \"टैरो\",\n                        \"ज्योतिष\",\n                        \"आध्यात्मिकता\",\n                        \"रहस्यमय\",\n                        \"भविष्यवाणी\",\n                        \"राशिफल\",\n                        \"अंकशास्त्र\"\n                    ]\n                };\n            case \"ja\":\n                return {\n                    title: {\n                        template: \"%s | ミスティカル・インサイト\",\n                        default: \"ミスティカル・インサイト - タロット、占星術、スピリチュアルガイダンス\"\n                    },\n                    description: \"プロのタロットリーディング、占星術の洞察、スピリチュアルガイダンスであなたの運命を発見してください。私たちの包括的なミスティカルサービスで宇宙の神秘を探求してください。\",\n                    keywords: [\n                        \"タロット\",\n                        \"占星術\",\n                        \"スピリチュアリティ\",\n                        \"ミスティカル\",\n                        \"占い\",\n                        \"ホロスコープ\",\n                        \"数秘術\"\n                    ]\n                };\n            default:\n                return {\n                    title: {\n                        template: \"%s | Mystical Insights\",\n                        default: \"Mystical Insights - Tarot, Astrology & Spiritual Guidance\"\n                    },\n                    description: \"Discover your destiny with professional tarot readings, astrology insights, and spiritual guidance. Explore the mysteries of the universe with our comprehensive mystical services.\",\n                    keywords: [\n                        \"tarot\",\n                        \"astrology\",\n                        \"spirituality\",\n                        \"mystical\",\n                        \"divination\",\n                        \"horoscope\",\n                        \"numerology\"\n                    ]\n                };\n        }\n    };\n    const localizedMeta = getLocalizedMetadata(locale);\n    return {\n        ...localizedMeta,\n        authors: [\n            {\n                name: \"Mystical Insights Team\"\n            }\n        ],\n        creator: \"Mystical Insights\",\n        publisher: \"Mystical Insights\",\n        formatDetection: {\n            email: false,\n            address: false,\n            telephone: false\n        },\n        metadataBase: new URL(baseUrl),\n        alternates: {\n            canonical: locale === \"en\" ? \"/\" : `/${locale}`,\n            languages: hreflangMap\n        },\n        openGraph: {\n            type: \"website\",\n            locale: locale === \"zh\" ? \"zh_CN\" : locale === \"ja\" ? \"ja_JP\" : `${locale}_${localeConfig.region}`,\n            url: locale === \"en\" ? \"/\" : `/${locale}`,\n            title: localizedMeta.title.default,\n            description: localizedMeta.description,\n            siteName: locale === \"zh\" ? \"神秘洞察\" : locale === \"es\" ? \"Perspectivas M\\xedsticas\" : locale === \"pt\" ? \"Percep\\xe7\\xf5es M\\xedsticas\" : locale === \"hi\" ? \"रहस्यमय अंतर्दृष्टि\" : locale === \"ja\" ? \"ミスティカル・インサイト\" : \"Mystical Insights\"\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: localizedMeta.title.default,\n            description: localizedMeta.description,\n            creator: \"@mysticalinsights\"\n        },\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                \"max-video-preview\": -1,\n                \"max-image-preview\": \"large\",\n                \"max-snippet\": -1\n            }\n        },\n        verification: {\n            google: process.env.GOOGLE_VERIFICATION,\n            yandex: process.env.YANDEX_VERIFICATION,\n            yahoo: process.env.YAHOO_VERIFICATION\n        }\n    };\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    // 验证语言\n    if (!(0,_i18n__WEBPACK_IMPORTED_MODULE_2__.isValidLocale)(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // 获取翻译消息\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const localeConfig = (0,_i18n__WEBPACK_IMPORTED_MODULE_2__.getLocaleConfig)(locale);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: localeConfig.dir,\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                messages: messages,\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/page.tsx":
/*!***********************************!*\
  !*** ./src/app/[locale]/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\");\n/* harmony import */ var _components_layout_page_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/page-layout */ \"(rsc)/./src/components/layout/page-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n\n\n\n\n\n\nasync function generateMetadata({ params: { locale } }) {\n    const t = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        locale,\n        namespace: \"seo.home\"\n    });\n    return {\n        title: t(\"title\"),\n        description: t(\"description\")\n    };\n}\nfunction HomePage({ params: { locale } }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_page_layout__WEBPACK_IMPORTED_MODULE_1__.PageLayout, {\n        locale: locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-mystical-50 via-cosmic-50 to-mystical-100 dark:from-mystical-950 dark:via-cosmic-950 dark:to-mystical-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-responsive-xl font-serif font-bold text-gradient mb-6\",\n                            children: t(\"hero.title\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-responsive-md text-muted-foreground mb-8 max-w-3xl mx-auto\",\n                            children: t(\"hero.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    variant: \"mystical\",\n                                    hover: true,\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto mb-4 bg-mystical-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl text-white\",\n                                                        children: \"\\uD83D\\uDD2E\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                        lineNumber: 44,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: t(\"hero.features.tarot.title\")\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: t(\"hero.features.tarot.description\")\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    variant: \"mystical\",\n                                    hover: true,\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto mb-4 bg-cosmic-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl text-white\",\n                                                        children: \"⭐\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: t(\"hero.features.astrology.title\")\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: t(\"hero.features.astrology.description\")\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    variant: \"mystical\",\n                                    hover: true,\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto mb-4 bg-mystical-600 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl text-white\",\n                                                        children: \"\\uD83C\\uDF19\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: t(\"hero.features.guidance.title\")\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: t(\"hero.features.guidance.description\")\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"mystical\",\n                                size: \"lg\",\n                                children: t(\"hero.cta\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\[locale]\\\\page.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-playfair\",\"display\":\"swap\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-playfair\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Fira_Code\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-fira-code\",\"display\":\"swap\"}],\"variableName\":\"firaCode\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Fira_Code\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-fira-code\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"firaCode\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: {\n        template: \"%s | Mystical Insights\",\n        default: \"Mystical Insights - Tarot, Astrology & Spiritual Guidance\"\n    },\n    description: \"Discover your destiny with professional tarot readings, astrology insights, and spiritual guidance. Explore the mysteries of the universe with our comprehensive mystical services.\",\n    keywords: [\n        \"tarot\",\n        \"astrology\",\n        \"spirituality\",\n        \"mystical\",\n        \"divination\",\n        \"horoscope\",\n        \"numerology\"\n    ],\n    authors: [\n        {\n            name: \"Mystical Insights Team\"\n        }\n    ],\n    creator: \"Mystical Insights\",\n    publisher: \"Mystical Insights\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Mystical Insights - Tarot, Astrology & Spiritual Guidance\",\n        description: \"Discover your destiny with professional tarot readings, astrology insights, and spiritual guidance.\",\n        siteName: \"Mystical Insights\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Mystical Insights - Tarot, Astrology & Spiritual Guidance\",\n        description: \"Discover your destiny with professional tarot readings, astrology insights, and spiritual guidance.\",\n        creator: \"@mysticalinsights\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: process.env.GOOGLE_VERIFICATION,\n        yandex: process.env.YANDEX_VERIFICATION,\n        yahoo: process.env.YAHOO_VERIFICATION\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#9c6eff\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#9c6eff\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_subsets_latin_variable_font_playfair_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Fira_Code_arguments_subsets_latin_variable_font_fira_code_display_swap_variableName_firaCode___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased`,\n                suppressHydrationWarning: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative min-h-screen bg-background\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBSU1BO0FBTUFDO0FBTUFDO0FBZHdCO0FBb0J2QixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztRQUNMQyxVQUFVO1FBQ1ZDLFNBQVM7SUFDWDtJQUNBQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFTO1FBQWE7UUFBZ0I7UUFBWTtRQUFjO1FBQWE7S0FBYTtJQUNyR0MsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBeUI7S0FBRTtJQUM3Q0MsU0FBUztJQUNUQyxXQUFXO0lBQ1hDLGlCQUFpQjtRQUNmQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsV0FBVztJQUNiO0lBQ0FDLGNBQWMsSUFBSUMsSUFBSUMsdUJBQWdDLElBQUk7SUFDMURHLFlBQVk7UUFDVkMsV0FBVztJQUNiO0lBQ0FDLFdBQVc7UUFDVEMsTUFBTTtRQUNOQyxRQUFRO1FBQ1JDLEtBQUs7UUFDTHZCLE9BQU87UUFDUEcsYUFBYTtRQUNicUIsVUFBVTtJQUNaO0lBQ0FDLFNBQVM7UUFDUEMsTUFBTTtRQUNOMUIsT0FBTztRQUNQRyxhQUFhO1FBQ2JJLFNBQVM7SUFDWDtJQUNBb0IsUUFBUTtRQUNOQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsV0FBVztZQUNURixPQUFPO1lBQ1BDLFFBQVE7WUFDUixxQkFBcUIsQ0FBQztZQUN0QixxQkFBcUI7WUFDckIsZUFBZSxDQUFDO1FBQ2xCO0lBQ0Y7SUFDQUUsY0FBYztRQUNaQyxRQUFRakIsUUFBUUMsR0FBRyxDQUFDaUIsbUJBQW1CO1FBQ3ZDQyxRQUFRbkIsUUFBUUMsR0FBRyxDQUFDbUIsbUJBQW1CO1FBQ3ZDQyxPQUFPckIsUUFBUUMsR0FBRyxDQUFDcUIsa0JBQWtCO0lBQ3ZDO0FBQ0YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyx3QkFBd0I7OzBCQUM1Qiw4REFBQ0M7O2tDQUNDLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7O2tDQUN0Qiw4REFBQ0Y7d0JBQUtDLEtBQUk7d0JBQW1CRSxPQUFNO3dCQUFVRCxNQUFLOzs7Ozs7a0NBQ2xELDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBT3ZCLE1BQUs7d0JBQVl5QixPQUFNO3dCQUFRRCxNQUFLOzs7Ozs7a0NBQ3JELDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBT3ZCLE1BQUs7d0JBQVl5QixPQUFNO3dCQUFRRCxNQUFLOzs7Ozs7a0NBQ3JELDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBV0MsTUFBSzs7Ozs7O2tDQUMxQiw4REFBQ0U7d0JBQUt6QyxNQUFLO3dCQUFjMEMsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Q7d0JBQUt6QyxNQUFLO3dCQUEwQjBDLFNBQVE7Ozs7Ozs7Ozs7OzswQkFFL0MsOERBQUNDO2dCQUNDQyxXQUFXLENBQUMsRUFBRXRELCtMQUFjLENBQUMsQ0FBQyxFQUFFQyxnTkFBaUIsQ0FBQyxDQUFDLEVBQUVDLDBNQUFpQixDQUFDLHNCQUFzQixDQUFDO2dCQUM5RjJDLHdCQUF3QjswQkFFeEIsNEVBQUNXO29CQUFJRixXQUFVOzhCQUNaWDs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL215c3RpY2FsLXdlYnNpdGUvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBJbnRlciwgUGxheWZhaXJfRGlzcGxheSwgRmlyYV9Db2RlIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSc7XG5pbXBvcnQgJ0Avc3R5bGVzL2dsb2JhbHMuY3NzJztcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7XG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtaW50ZXInLFxuICBkaXNwbGF5OiAnc3dhcCcsXG59KTtcblxuY29uc3QgcGxheWZhaXIgPSBQbGF5ZmFpcl9EaXNwbGF5KHtcbiAgc3Vic2V0czogWydsYXRpbiddLFxuICB2YXJpYWJsZTogJy0tZm9udC1wbGF5ZmFpcicsXG4gIGRpc3BsYXk6ICdzd2FwJyxcbn0pO1xuXG5jb25zdCBmaXJhQ29kZSA9IEZpcmFfQ29kZSh7XG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgdmFyaWFibGU6ICctLWZvbnQtZmlyYS1jb2RlJyxcbiAgZGlzcGxheTogJ3N3YXAnLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiB7XG4gICAgdGVtcGxhdGU6ICclcyB8IE15c3RpY2FsIEluc2lnaHRzJyxcbiAgICBkZWZhdWx0OiAnTXlzdGljYWwgSW5zaWdodHMgLSBUYXJvdCwgQXN0cm9sb2d5ICYgU3Bpcml0dWFsIEd1aWRhbmNlJyxcbiAgfSxcbiAgZGVzY3JpcHRpb246ICdEaXNjb3ZlciB5b3VyIGRlc3Rpbnkgd2l0aCBwcm9mZXNzaW9uYWwgdGFyb3QgcmVhZGluZ3MsIGFzdHJvbG9neSBpbnNpZ2h0cywgYW5kIHNwaXJpdHVhbCBndWlkYW5jZS4gRXhwbG9yZSB0aGUgbXlzdGVyaWVzIG9mIHRoZSB1bml2ZXJzZSB3aXRoIG91ciBjb21wcmVoZW5zaXZlIG15c3RpY2FsIHNlcnZpY2VzLicsXG4gIGtleXdvcmRzOiBbJ3Rhcm90JywgJ2FzdHJvbG9neScsICdzcGlyaXR1YWxpdHknLCAnbXlzdGljYWwnLCAnZGl2aW5hdGlvbicsICdob3Jvc2NvcGUnLCAnbnVtZXJvbG9neSddLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnTXlzdGljYWwgSW5zaWdodHMgVGVhbScgfV0sXG4gIGNyZWF0b3I6ICdNeXN0aWNhbCBJbnNpZ2h0cycsXG4gIHB1Ymxpc2hlcjogJ015c3RpY2FsIEluc2lnaHRzJyxcbiAgZm9ybWF0RGV0ZWN0aW9uOiB7XG4gICAgZW1haWw6IGZhbHNlLFxuICAgIGFkZHJlc3M6IGZhbHNlLFxuICAgIHRlbGVwaG9uZTogZmFsc2UsXG4gIH0sXG4gIG1ldGFkYXRhQmFzZTogbmV3IFVSTChwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TSVRFX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwJyksXG4gIGFsdGVybmF0ZXM6IHtcbiAgICBjYW5vbmljYWw6ICcvJyxcbiAgfSxcbiAgb3BlbkdyYXBoOiB7XG4gICAgdHlwZTogJ3dlYnNpdGUnLFxuICAgIGxvY2FsZTogJ2VuX1VTJyxcbiAgICB1cmw6ICcvJyxcbiAgICB0aXRsZTogJ015c3RpY2FsIEluc2lnaHRzIC0gVGFyb3QsIEFzdHJvbG9neSAmIFNwaXJpdHVhbCBHdWlkYW5jZScsXG4gICAgZGVzY3JpcHRpb246ICdEaXNjb3ZlciB5b3VyIGRlc3Rpbnkgd2l0aCBwcm9mZXNzaW9uYWwgdGFyb3QgcmVhZGluZ3MsIGFzdHJvbG9neSBpbnNpZ2h0cywgYW5kIHNwaXJpdHVhbCBndWlkYW5jZS4nLFxuICAgIHNpdGVOYW1lOiAnTXlzdGljYWwgSW5zaWdodHMnLFxuICB9LFxuICB0d2l0dGVyOiB7XG4gICAgY2FyZDogJ3N1bW1hcnlfbGFyZ2VfaW1hZ2UnLFxuICAgIHRpdGxlOiAnTXlzdGljYWwgSW5zaWdodHMgLSBUYXJvdCwgQXN0cm9sb2d5ICYgU3Bpcml0dWFsIEd1aWRhbmNlJyxcbiAgICBkZXNjcmlwdGlvbjogJ0Rpc2NvdmVyIHlvdXIgZGVzdGlueSB3aXRoIHByb2Zlc3Npb25hbCB0YXJvdCByZWFkaW5ncywgYXN0cm9sb2d5IGluc2lnaHRzLCBhbmQgc3Bpcml0dWFsIGd1aWRhbmNlLicsXG4gICAgY3JlYXRvcjogJ0BteXN0aWNhbGluc2lnaHRzJyxcbiAgfSxcbiAgcm9ib3RzOiB7XG4gICAgaW5kZXg6IHRydWUsXG4gICAgZm9sbG93OiB0cnVlLFxuICAgIGdvb2dsZUJvdDoge1xuICAgICAgaW5kZXg6IHRydWUsXG4gICAgICBmb2xsb3c6IHRydWUsXG4gICAgICAnbWF4LXZpZGVvLXByZXZpZXcnOiAtMSxcbiAgICAgICdtYXgtaW1hZ2UtcHJldmlldyc6ICdsYXJnZScsXG4gICAgICAnbWF4LXNuaXBwZXQnOiAtMSxcbiAgICB9LFxuICB9LFxuICB2ZXJpZmljYXRpb246IHtcbiAgICBnb29nbGU6IHByb2Nlc3MuZW52LkdPT0dMRV9WRVJJRklDQVRJT04sXG4gICAgeWFuZGV4OiBwcm9jZXNzLmVudi5ZQU5ERVhfVkVSSUZJQ0FUSU9OLFxuICAgIHlhaG9vOiBwcm9jZXNzLmVudi5ZQUhPT19WRVJJRklDQVRJT04sXG4gIH0sXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiYXBwbGUtdG91Y2gtaWNvblwiIHNpemVzPVwiMTgweDE4MFwiIGhyZWY9XCIvYXBwbGUtdG91Y2gtaWNvbi5wbmdcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgdHlwZT1cImltYWdlL3BuZ1wiIHNpemVzPVwiMzJ4MzJcIiBocmVmPVwiL2Zhdmljb24tMzJ4MzIucG5nXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIHR5cGU9XCJpbWFnZS9wbmdcIiBzaXplcz1cIjE2eDE2XCIgaHJlZj1cIi9mYXZpY29uLTE2eDE2LnBuZ1wiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cIm1hbmlmZXN0XCIgaHJlZj1cIi9zaXRlLndlYm1hbmlmZXN0XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInRoZW1lLWNvbG9yXCIgY29udGVudD1cIiM5YzZlZmZcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwibXNhcHBsaWNhdGlvbi1UaWxlQ29sb3JcIiBjb250ZW50PVwiIzljNmVmZlwiIC8+XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2ludGVyLnZhcmlhYmxlfSAke3BsYXlmYWlyLnZhcmlhYmxlfSAke2ZpcmFDb2RlLnZhcmlhYmxlfSBmb250LXNhbnMgYW50aWFsaWFzZWRgfVxuICAgICAgICBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmdcbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtaW4taC1zY3JlZW4gYmctYmFja2dyb3VuZFwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwicGxheWZhaXIiLCJmaXJhQ29kZSIsIm1ldGFkYXRhIiwidGl0bGUiLCJ0ZW1wbGF0ZSIsImRlZmF1bHQiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJjcmVhdG9yIiwicHVibGlzaGVyIiwiZm9ybWF0RGV0ZWN0aW9uIiwiZW1haWwiLCJhZGRyZXNzIiwidGVsZXBob25lIiwibWV0YWRhdGFCYXNlIiwiVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NJVEVfVVJMIiwiYWx0ZXJuYXRlcyIsImNhbm9uaWNhbCIsIm9wZW5HcmFwaCIsInR5cGUiLCJsb2NhbGUiLCJ1cmwiLCJzaXRlTmFtZSIsInR3aXR0ZXIiLCJjYXJkIiwicm9ib3RzIiwiaW5kZXgiLCJmb2xsb3ciLCJnb29nbGVCb3QiLCJ2ZXJpZmljYXRpb24iLCJnb29nbGUiLCJHT09HTEVfVkVSSUZJQ0FUSU9OIiwieWFuZGV4IiwiWUFOREVYX1ZFUklGSUNBVElPTiIsInlhaG9vIiwiWUFIT09fVkVSSUZJQ0FUSU9OIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImhlYWQiLCJsaW5rIiwicmVsIiwiaHJlZiIsInNpemVzIiwibWV0YSIsImNvbnRlbnQiLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiLCJkaXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\n\nfunction Footer({ locale, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const getHref = (href)=>{\n        return locale === \"en\" ? href : `/${locale}${href}`;\n    };\n    const quickLinks = [\n        {\n            key: \"home\",\n            href: \"/\"\n        },\n        {\n            key: \"blog\",\n            href: \"/blog\"\n        },\n        {\n            key: \"about\",\n            href: \"/about\"\n        },\n        {\n            key: \"contact\",\n            href: \"/contact\"\n        }\n    ];\n    const services = [\n        {\n            key: \"tarot\",\n            href: \"/tarot\"\n        },\n        {\n            key: \"astrology\",\n            href: \"/astrology\"\n        },\n        {\n            key: \"numerology\",\n            href: \"/numerology\"\n        },\n        {\n            key: \"tests\",\n            href: \"/tests\"\n        }\n    ];\n    const support = [\n        {\n            key: \"privacy\",\n            href: \"/privacy\"\n        },\n        {\n            key: \"terms\",\n            href: \"/terms\"\n        },\n        {\n            key: \"contact\",\n            href: \"/contact\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-muted/30 border-t border-border/50\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-br from-mystical-500 to-cosmic-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg font-bold\",\n                                                children: \"M\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-bold bg-gradient-to-r from-mystical-600 to-cosmic-600 bg-clip-text text-transparent\",\n                                            children: locale === \"zh\" ? \"神秘洞察\" : locale === \"es\" ? \"Perspectivas M\\xedsticas\" : locale === \"pt\" ? \"Percep\\xe7\\xf5es M\\xedsticas\" : locale === \"hi\" ? \"रहस्यमय अंतर्दृष्टि\" : locale === \"ja\" ? \"ミスティカル・インサイト\" : \"Mystical Insights\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground max-w-xs\",\n                                    children: t(\"footer.description\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                \"aria-label\": \"Facebook\",\n                                                children: \"\\uD83D\\uDCD8\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                \"aria-label\": \"Twitter\",\n                                                children: \"\\uD83D\\uDC26\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                \"aria-label\": \"Instagram\",\n                                                children: \"\\uD83D\\uDCF7\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                \"aria-label\": \"YouTube\",\n                                                children: \"\\uD83D\\uDCFA\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold uppercase tracking-wider\",\n                                    children: t(\"footer.quick_links\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: getHref(link.href),\n                                                className: \"text-sm text-muted-foreground hover:text-foreground transition-colors\",\n                                                children: t(`navigation.${link.key}`)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.key, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold uppercase tracking-wider\",\n                                    children: t(\"footer.services\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: getHref(service.href),\n                                                className: \"text-sm text-muted-foreground hover:text-foreground transition-colors\",\n                                                children: t(`navigation.${service.key}`)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, service.key, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold uppercase tracking-wider\",\n                                    children: t(\"footer.newsletter\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: t(\"footer.newsletter_description\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: t(\"footer.email_placeholder\"),\n                                            className: \"w-full px-3 py-2 text-sm bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"mystical\",\n                                            size: \"sm\",\n                                            className: \"w-full\",\n                                            children: t(\"footer.subscribe\")\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-border/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: t(\"footer.copyright\")\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: getHref(link.href),\n                                        className: \"text-sm text-muted-foreground hover:text-foreground transition-colors\",\n                                        children: t(`common.${link.key}`)\n                                    }, link.key, false, {\n                                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   Header: () => (/* binding */ e0),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\tarot-seo\src\components\layout\header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\tarot-seo\src\components\layout\header.tsx#Header`);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\tarot-seo\src\components\layout\header.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/layout/page-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/page-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageLayout: () => (/* binding */ PageLayout),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./header */ \"(rsc)/./src/components/layout/header.tsx\");\n/* harmony import */ var _footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./footer */ \"(rsc)/./src/components/layout/footer.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nfunction PageLayout({ children, locale, className, headerClassName, footerClassName }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"min-h-screen flex flex-col\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_2__.Header, {\n                locale: locale,\n                className: headerClassName\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\page-layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\page-layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {\n                locale: locale,\n                className: footerClassName\n            }, void 0, false, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\page-layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\layout\\\\page-layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvcGFnZS1sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDRztBQUNBO0FBQ0Q7QUFXMUIsU0FBU0ksV0FBVyxFQUN6QkMsUUFBUSxFQUNSQyxNQUFNLEVBQ05DLFNBQVMsRUFDVEMsZUFBZSxFQUNmQyxlQUFlLEVBQ0M7SUFDaEIscUJBQ0UsOERBQUNDO1FBQUlILFdBQVdKLDhDQUFFQSxDQUFDLDhCQUE4Qkk7OzBCQUMvQyw4REFBQ04sMkNBQU1BO2dCQUFDSyxRQUFRQTtnQkFBUUMsV0FBV0M7Ozs7OzswQkFDbkMsOERBQUNHO2dCQUFLSixXQUFVOzBCQUNiRjs7Ozs7OzBCQUVILDhEQUFDSCwyQ0FBTUE7Z0JBQUNJLFFBQVFBO2dCQUFRQyxXQUFXRTs7Ozs7Ozs7Ozs7O0FBR3pDO0FBRUEsaUVBQWVMLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teXN0aWNhbC13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L3BhZ2UtbGF5b3V0LnRzeD9jZGI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEhlYWRlciB9IGZyb20gJy4vaGVhZGVyJztcbmltcG9ydCB7IEZvb3RlciB9IGZyb20gJy4vZm9vdGVyJztcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9saWIvdXRpbHMnO1xuaW1wb3J0IHR5cGUgeyBMb2NhbGUgfSBmcm9tICdAL3R5cGVzJztcblxuaW50ZXJmYWNlIFBhZ2VMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIGxvY2FsZTogTG9jYWxlO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIGhlYWRlckNsYXNzTmFtZT86IHN0cmluZztcbiAgZm9vdGVyQ2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gUGFnZUxheW91dCh7XG4gIGNoaWxkcmVuLFxuICBsb2NhbGUsXG4gIGNsYXNzTmFtZSxcbiAgaGVhZGVyQ2xhc3NOYW1lLFxuICBmb290ZXJDbGFzc05hbWUsXG59OiBQYWdlTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y24oJ21pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sJywgY2xhc3NOYW1lKX0+XG4gICAgICA8SGVhZGVyIGxvY2FsZT17bG9jYWxlfSBjbGFzc05hbWU9e2hlYWRlckNsYXNzTmFtZX0gLz5cbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L21haW4+XG4gICAgICA8Rm9vdGVyIGxvY2FsZT17bG9jYWxlfSBjbGFzc05hbWU9e2Zvb3RlckNsYXNzTmFtZX0gLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgUGFnZUxheW91dDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkhlYWRlciIsIkZvb3RlciIsImNuIiwiUGFnZUxheW91dCIsImNoaWxkcmVuIiwibG9jYWxlIiwiY2xhc3NOYW1lIiwiaGVhZGVyQ2xhc3NOYW1lIiwiZm9vdGVyQ2xhc3NOYW1lIiwiZGl2IiwibWFpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/page-layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            mystical: \"bg-gradient-to-r from-mystical-600 to-cosmic-600 text-white hover:from-mystical-700 hover:to-cosmic-700 shadow-lg hover:shadow-xl transition-all duration-300\",\n            cosmic: \"bg-gradient-to-r from-cosmic-500 to-mystical-500 text-white hover:from-cosmic-600 hover:to-mystical-600 shadow-lg hover:shadow-xl transition-all duration-300\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            xl: \"h-12 rounded-lg px-10 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 57,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 50,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", {\n    variants: {\n        variant: {\n            default: \"border-border\",\n            mystical: \"bg-card/50 backdrop-blur-sm border-border/50 shadow-lg hover:shadow-xl transition-all duration-300\",\n            cosmic: \"bg-gradient-to-br from-cosmic-50/50 to-mystical-50/50 border-cosmic-200/50 shadow-lg\",\n            glass: \"bg-card/30 backdrop-blur-md border-border/30 shadow-xl\"\n        },\n        padding: {\n            none: \"\",\n            sm: \"p-4\",\n            default: \"p-6\",\n            lg: \"p-8\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        padding: \"default\"\n    }\n});\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, padding, hover = false, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(cardVariants({\n            variant,\n            padding\n        }), hover && \"hover:-translate-y-1 transition-transform duration-300\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 101,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/i18n.ts":
/*!*********************!*\
  !*** ./src/i18n.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   detectLocaleFromHeaders: () => (/* binding */ detectLocaleFromHeaders),\n/* harmony export */   generateHreflangMap: () => (/* binding */ generateHreflangMap),\n/* harmony export */   getLocaleConfig: () => (/* binding */ getLocaleConfig),\n/* harmony export */   getLocaleFromPath: () => (/* binding */ getLocaleFromPath),\n/* harmony export */   getLocalePath: () => (/* binding */ getLocalePath),\n/* harmony export */   getLocalizedLanguageName: () => (/* binding */ getLocalizedLanguageName),\n/* harmony export */   isValidLocale: () => (/* binding */ isValidLocale),\n/* harmony export */   localeConfig: () => (/* binding */ localeConfig),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n\n// 支持的语言列表\nconst locales = [\n    \"en\",\n    \"zh\",\n    \"es\",\n    \"pt\",\n    \"hi\",\n    \"ja\"\n];\n// 默认语言\nconst defaultLocale = \"en\";\n// 语言配置映射\nconst localeConfig = {\n    en: {\n        name: \"English\",\n        flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n        dir: \"ltr\",\n        region: \"US\",\n        currency: \"USD\",\n        dateFormat: \"MM/dd/yyyy\"\n    },\n    zh: {\n        name: \"中文\",\n        flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\",\n        dir: \"ltr\",\n        region: \"CN\",\n        currency: \"CNY\",\n        dateFormat: \"yyyy年MM月dd日\"\n    },\n    es: {\n        name: \"Espa\\xf1ol\",\n        flag: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n        dir: \"ltr\",\n        region: \"ES\",\n        currency: \"EUR\",\n        dateFormat: \"dd/MM/yyyy\"\n    },\n    pt: {\n        name: \"Portugu\\xeas\",\n        flag: \"\\uD83C\\uDDE7\\uD83C\\uDDF7\",\n        dir: \"ltr\",\n        region: \"BR\",\n        currency: \"BRL\",\n        dateFormat: \"dd/MM/yyyy\"\n    },\n    hi: {\n        name: \"हिन्दी\",\n        flag: \"\\uD83C\\uDDEE\\uD83C\\uDDF3\",\n        dir: \"ltr\",\n        region: \"IN\",\n        currency: \"INR\",\n        dateFormat: \"dd/MM/yyyy\"\n    },\n    ja: {\n        name: \"日本語\",\n        flag: \"\\uD83C\\uDDEF\\uD83C\\uDDF5\",\n        dir: \"ltr\",\n        region: \"JP\",\n        currency: \"JPY\",\n        dateFormat: \"yyyy年MM月dd日\"\n    }\n};\n// 验证语言是否支持\nfunction isValidLocale(locale) {\n    return locales.includes(locale);\n}\n// 获取语言配置\nfunction getLocaleConfig(locale) {\n    return localeConfig[locale];\n}\n// 获取语言的完整URL路径\nfunction getLocalePath(locale, path = \"\") {\n    const cleanPath = path.startsWith(\"/\") ? path.slice(1) : path;\n    return locale === defaultLocale ? `/${cleanPath}` : `/${locale}/${cleanPath}`;\n}\n// 从路径中提取语言\nfunction getLocaleFromPath(path) {\n    const segments = path.split(\"/\").filter(Boolean);\n    const firstSegment = segments[0];\n    if (firstSegment && isValidLocale(firstSegment)) {\n        return firstSegment;\n    }\n    return defaultLocale;\n}\n// 生成hreflang映射\nfunction generateHreflangMap(path, baseUrl) {\n    const hreflangMap = {};\n    locales.forEach((locale)=>{\n        const localePath = getLocalePath(locale, path);\n        hreflangMap[locale] = `${baseUrl}${localePath}`;\n    });\n    // 添加x-default\n    hreflangMap[\"x-default\"] = `${baseUrl}${getLocalePath(defaultLocale, path)}`;\n    return hreflangMap;\n}\n// next-intl配置\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ locale })=>{\n    // 验证传入的语言是否支持\n    if (!isValidLocale(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n    try {\n        return {\n            messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default,\n            timeZone: getTimeZoneForLocale(locale),\n            now: new Date()\n        };\n    } catch (error) {\n        console.error(`Failed to load messages for locale: ${locale}`, error);\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)();\n    }\n}));\n// 根据语言获取时区\nfunction getTimeZoneForLocale(locale) {\n    const timezoneMap = {\n        en: \"America/New_York\",\n        zh: \"Asia/Shanghai\",\n        es: \"Europe/Madrid\",\n        pt: \"America/Sao_Paulo\",\n        hi: \"Asia/Kolkata\",\n        ja: \"Asia/Tokyo\"\n    };\n    return timezoneMap[locale] || \"UTC\";\n}\n// 语言检测和重定向逻辑\nfunction detectLocaleFromHeaders(acceptLanguage) {\n    if (!acceptLanguage) return defaultLocale;\n    // 解析Accept-Language头\n    const languages = acceptLanguage.split(\",\").map((lang)=>{\n        const [code, q = \"1\"] = lang.trim().split(\";q=\");\n        return {\n            code: code.toLowerCase(),\n            quality: parseFloat(q)\n        };\n    }).sort((a, b)=>b.quality - a.quality);\n    // 查找匹配的语言\n    for (const { code } of languages){\n        // 精确匹配\n        if (isValidLocale(code)) {\n            return code;\n        }\n        // 语言代码匹配（如 zh-CN -> zh）\n        const langCode = code.split(\"-\")[0];\n        if (isValidLocale(langCode)) {\n            return langCode;\n        }\n    }\n    return defaultLocale;\n}\n// 获取语言的本地化名称\nfunction getLocalizedLanguageName(locale, targetLocale) {\n    const names = {\n        en: {\n            en: \"English\",\n            zh: \"Chinese\",\n            es: \"Spanish\",\n            pt: \"Portuguese\",\n            hi: \"Hindi\",\n            ja: \"Japanese\"\n        },\n        zh: {\n            en: \"英语\",\n            zh: \"中文\",\n            es: \"西班牙语\",\n            pt: \"葡萄牙语\",\n            hi: \"印地语\",\n            ja: \"日语\"\n        },\n        es: {\n            en: \"Ingl\\xe9s\",\n            zh: \"Chino\",\n            es: \"Espa\\xf1ol\",\n            pt: \"Portugu\\xe9s\",\n            hi: \"Hindi\",\n            ja: \"Japon\\xe9s\"\n        },\n        pt: {\n            en: \"Ingl\\xeas\",\n            zh: \"Chin\\xeas\",\n            es: \"Espanhol\",\n            pt: \"Portugu\\xeas\",\n            hi: \"Hindi\",\n            ja: \"Japon\\xeas\"\n        },\n        hi: {\n            en: \"अंग्रेजी\",\n            zh: \"चीनी\",\n            es: \"स्पेनिश\",\n            pt: \"पुर्तगाली\",\n            hi: \"हिन्दी\",\n            ja: \"जापानी\"\n        },\n        ja: {\n            en: \"英語\",\n            zh: \"中国語\",\n            es: \"スペイン語\",\n            pt: \"ポルトガル語\",\n            hi: \"ヒンディー語\",\n            ja: \"日本語\"\n        }\n    };\n    return names[targetLocale]?.[locale] || localeConfig[locale].name;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildUrl: () => (/* binding */ buildUrl),\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepMerge: () => (/* binding */ deepMerge),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getRandomElement: () => (/* binding */ getRandomElement),\n/* harmony export */   getUrlParams: () => (/* binding */ getUrlParams),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   shuffleArray: () => (/* binding */ shuffleArray),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * 合并Tailwind CSS类名\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * 格式化日期\n */ function formatDate(date, locale = \"en\", options = {}) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const defaultOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n        ...options\n    };\n    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);\n}\n/**\n * 格式化相对时间\n */ function formatRelativeTime(date, locale = \"en\") {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    const rtf = new Intl.RelativeTimeFormat(locale, {\n        numeric: \"auto\"\n    });\n    if (diffInSeconds < 60) {\n        return rtf.format(-diffInSeconds, \"second\");\n    } else if (diffInSeconds < 3600) {\n        return rtf.format(-Math.floor(diffInSeconds / 60), \"minute\");\n    } else if (diffInSeconds < 86400) {\n        return rtf.format(-Math.floor(diffInSeconds / 3600), \"hour\");\n    } else if (diffInSeconds < 2592000) {\n        return rtf.format(-Math.floor(diffInSeconds / 86400), \"day\");\n    } else if (diffInSeconds < 31536000) {\n        return rtf.format(-Math.floor(diffInSeconds / 2592000), \"month\");\n    } else {\n        return rtf.format(-Math.floor(diffInSeconds / 31536000), \"year\");\n    }\n}\n/**\n * 生成URL友好的slug\n */ function generateSlug(text) {\n    return text.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // 移除特殊字符\n    .replace(/[\\s_-]+/g, \"-\") // 替换空格和下划线为连字符\n    .replace(/^-+|-+$/g, \"\"); // 移除开头和结尾的连字符\n}\n/**\n * 截断文本\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + \"...\";\n}\n/**\n * 计算阅读时间（分钟）\n */ function calculateReadingTime(text) {\n    const wordsPerMinute = 200; // 平均阅读速度\n    const words = text.trim().split(/\\s+/).length;\n    return Math.ceil(words / wordsPerMinute);\n}\n/**\n * 验证邮箱格式\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * 生成随机ID\n */ function generateId(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * 深度合并对象\n */ function deepMerge(target, source) {\n    const result = {\n        ...target\n    };\n    for(const key in source){\n        if (source[key] && typeof source[key] === \"object\" && !Array.isArray(source[key])) {\n            result[key] = deepMerge(result[key] || {}, source[key]);\n        } else {\n            result[key] = source[key];\n        }\n    }\n    return result;\n}\n/**\n * 防抖函数\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * 节流函数\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * 格式化数字\n */ function formatNumber(num, locale = \"en\", options = {}) {\n    return new Intl.NumberFormat(locale, options).format(num);\n}\n/**\n * 格式化货币\n */ function formatCurrency(amount, currency = \"USD\", locale = \"en\") {\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n/**\n * 获取随机数组元素\n */ function getRandomElement(array) {\n    return array[Math.floor(Math.random() * array.length)];\n}\n/**\n * 打乱数组\n */ function shuffleArray(array) {\n    const shuffled = [\n        ...array\n    ];\n    for(let i = shuffled.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [shuffled[i], shuffled[j]] = [\n            shuffled[j],\n            shuffled[i]\n        ];\n    }\n    return shuffled;\n}\n/**\n * 检查是否为移动设备\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * 获取设备类型\n */ function getDeviceType() {\n    if (true) return \"desktop\";\n    const width = window.innerWidth;\n    if (width < 768) return \"mobile\";\n    if (width < 1024) return \"tablet\";\n    return \"desktop\";\n}\n/**\n * 复制到剪贴板\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (error) {\n        console.error(\"Failed to copy to clipboard:\", error);\n        return false;\n    }\n}\n/**\n * 获取URL参数\n */ function getUrlParams(url) {\n    const params = new URLSearchParams(new URL(url).search);\n    const result = {};\n    for (const [key, value] of params.entries()){\n        result[key] = value;\n    }\n    return result;\n}\n/**\n * 构建URL\n */ function buildUrl(base, params) {\n    const url = new URL(base,  false ? 0 : \"http://localhost:3000\");\n    Object.entries(params).forEach(([key, value])=>{\n        if (value !== undefined && value !== null) {\n            url.searchParams.set(key, String(value));\n        }\n    });\n    return url.toString();\n}\n/**\n * 安全的JSON解析\n */ function safeJsonParse(json, fallback) {\n    try {\n        return JSON.parse(json);\n    } catch  {\n        return fallback;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@opentelemetry","vendor-chunks/next-intl","vendor-chunks/@formatjs","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/@radix-ui","vendor-chunks/tslib","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fpage&page=%2F%5Blocale%5D%2Fpage&appPaths=%2F%5Blocale%5D%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fpage.tsx&appDir=E%3A%5Ctarot-seo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Ctarot-seo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();